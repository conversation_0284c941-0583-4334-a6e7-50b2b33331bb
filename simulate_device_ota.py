#!/usr/bin/env python3
"""
设备端OTA请求模拟脚本

模拟ESP32设备向服务器发送OTA请求，测试不同认证模式下的响应处理。
根据设备端固件代码分析：
1. 设备发送OTA请求到Java后端
2. 如果返回activation字段，设备会显示激活码并等待用户激活
3. 如果返回websocket字段，设备会连接WebSocket服务
4. 如果没有activation字段，设备会直接连接WebSocket
"""

import json
import requests
import websockets
import asyncio
import argparse
import sys
from typing import Dict, Any, Optional


class DeviceOTASimulator:
    def __init__(self, server_url: str = "http://localhost:8002", 
                 websocket_url: str = "ws://localhost:8000",
                 mac_address: str = "FF:FF:FF:FF:FF:FF"):
        self.server_url = server_url
        self.websocket_url = websocket_url
        self.mac_address = mac_address
        self.device_id = mac_address
        
    def create_ota_request(self) -> Dict[str, Any]:
        """创建OTA请求数据，模拟ESP32设备发送的JSON"""
        return {
            "mac_address": self.mac_address,
            "board": {
                "type": "esp32"
            },
            "application": {
                "version": "1.0.0"
            },
            "chip_model_name": "ESP32-S3"
        }
    
    def create_headers(self) -> Dict[str, str]:
        """创建HTTP请求头，模拟ESP32设备"""
        return {
            "Content-Type": "application/json",
            "Device-Id": self.device_id,
            "Client-Id": "test-device-uuid",
            "User-Agent": "ESP32-S3/1.0.0",
            "Accept-Language": "zh-CN",
            "Activation-Version": "1"
        }
    
    def send_ota_request(self) -> Optional[Dict[str, Any]]:
        """发送OTA请求到Java后端"""
        url = f"{self.server_url}/xiaozhi/ota/"
        headers = self.create_headers()
        data = self.create_ota_request()
        
        print(f"🚀 发送OTA请求到: {url}")
        print(f"📱 设备MAC地址: {self.mac_address}")
        print(f"📦 请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"📋 请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print("-" * 60)
        
        try:
            response = requests.post(url, json=data, headers=headers, timeout=10)
            print(f"📡 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                return response_data
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return None
    
    def analyze_response(self, response_data: Dict[str, Any]) -> str:
        """分析OTA响应，模拟设备端的处理逻辑"""
        print("\n" + "="*60)
        print("📊 设备端响应分析")
        print("="*60)
        
        # 检查激活码字段
        if "activation" in response_data:
            activation = response_data["activation"]
            print("🔐 检测到激活码字段:")
            print(f"   - 激活码: {activation.get('code', 'N/A')}")
            print(f"   - 消息: {activation.get('message', 'N/A')}")
            print(f"   - 挑战码: {activation.get('challenge', 'N/A')}")
            print("\n🎯 设备端行为:")
            print("   1. 显示激活码给用户")
            print("   2. 播放激活码语音")
            print("   3. 等待用户在网页上输入激活码")
            print("   4. 定期重试激活请求")
            return "ACTIVATION_REQUIRED"
        
        # 检查WebSocket字段
        elif "websocket" in response_data:
            websocket = response_data["websocket"]
            websocket_url = websocket.get("url", "")
            print("🌐 检测到WebSocket配置:")
            print(f"   - WebSocket URL: {websocket_url}")
            print("\n🎯 设备端行为:")
            print("   1. 直接连接WebSocket服务")
            print("   2. 开始语音交互功能")
            print("   3. 设备已激活，正常工作")
            return "WEBSOCKET_CONNECT"
        
        # 检查错误字段
        elif "error" in response_data:
            error = response_data["error"]
            print(f"❌ 检测到错误信息: {error}")
            print("\n🎯 设备端行为:")
            print("   1. 显示错误信息")
            print("   2. 可能重试OTA请求")
            return "ERROR"
        
        else:
            print("⚠️  未检测到预期的字段")
            print("\n🎯 设备端行为:")
            print("   1. 可能使用默认配置")
            print("   2. 尝试连接默认WebSocket地址")
            return "UNKNOWN"
    
    async def test_websocket_connection(self, websocket_url: str):
        """测试WebSocket连接"""
        print(f"\n🔌 尝试连接WebSocket: {websocket_url}")
        
        try:
            headers = {
                "Device-Id": self.device_id,
                "Client-Id": "test-device-uuid"
            }
            
            async with websockets.connect(websocket_url, extra_headers=headers) as websocket:
                print("✅ WebSocket连接成功!")
                print("📤 发送测试消息...")
                
                # 发送设备信息
                device_info = {
                    "type": "device_info",
                    "mac_address": self.mac_address,
                    "board_type": "esp32",
                    "app_version": "1.0.0"
                }
                
                await websocket.send(json.dumps(device_info))
                print(f"📤 已发送: {json.dumps(device_info, ensure_ascii=False)}")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"📥 收到响应: {response}")
                except asyncio.TimeoutError:
                    print("⏰ 等待响应超时")
                    
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
    
    async def run_simulation(self):
        """运行完整的设备模拟"""
        print("🤖 ESP32设备OTA模拟器")
        print("="*60)
        
        # 1. 发送OTA请求
        response_data = self.send_ota_request()
        if not response_data:
            return
        
        # 2. 分析响应
        behavior = self.analyze_response(response_data)
        
        # 3. 根据响应执行相应行为
        if behavior == "WEBSOCKET_CONNECT":
            websocket_url = response_data["websocket"]["url"]
            await self.test_websocket_connection(websocket_url)
        elif behavior == "ACTIVATION_REQUIRED":
            print("\n💡 提示: 设备正在等待激活，请:")
            print("   1. 记录显示的激活码")
            print("   2. 访问管理后台")
            print("   3. 在设备管理页面输入激活码")
        
        print("\n🏁 模拟完成")


def main():
    parser = argparse.ArgumentParser(description="ESP32设备OTA请求模拟器")
    parser.add_argument("--server", default="http://localhost:8002", 
                       help="服务器地址 (默认: http://localhost:8002)")
    parser.add_argument("--websocket", default="ws://localhost:8000", 
                       help="WebSocket地址 (默认: ws://localhost:8000)")
    parser.add_argument("--mac", default="FF:FF:FF:FF:FF:FF", 
                       help="设备MAC地址 (默认: FF:FF:FF:FF:FF:FF)")
    
    args = parser.parse_args()
    
    simulator = DeviceOTASimulator(
        server_url=args.server,
        websocket_url=args.websocket,
        mac_address=args.mac
    )
    
    try:
        asyncio.run(simulator.run_simulation())
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出模拟器")
    except Exception as e:
        print(f"\n❌ 模拟器异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
