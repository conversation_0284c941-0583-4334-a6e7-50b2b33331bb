# Docker 构建缓存优化架构设计方案

## 项目背景

小智ESP32服务器项目采用Java/Spring Boot + Python混合架构，Python模块包含大量重型ML依赖（torch、torchaudio、funasr等），在Mac M1 → AMD64跨平台构建时存在缓存效率问题。

## 现状分析

### 当前缓存结构
```
.docker_cache/
├── apt/          # 系统包缓存
├── npm/          # Node.js包缓存  
└── pip/          # Python包缓存
```

### 重型依赖识别
- **torch==2.2.2** (~2.5GB)
- **torchaudio==2.2.2** (~300MB)
- **funasr==1.2.3** (~500MB)
- **modelscope==1.23.2** (~200MB)
- **sherpa_onnx==1.11.0** (~150MB)

### 核心问题
1. Mac M1 → AMD64跨平台构建缓存失效
2. 重型ML依赖重复下载，构建时间长（20-40分钟）
3. Docker层缓存空间占用大，清理困难
4. 缓存策略不够智能，项目间缺乏隔离

## 架构设计方案

### 1. 优化目录结构设计

```
.docker_cache/                    # 复用现有缓存目录
├── apt/                         # 系统包缓存（保持现状）
├── npm/                         # Node.js包缓存（保持现状）
├── pip/                         # Python包缓存（保持现状）
└── cross-platform/              # 新增：跨平台缓存目录
    ├── wheels/                   # 预编译wheel文件缓存
    │   ├── linux-amd64/         # AMD64平台wheel
    │   ├── linux-arm64/         # ARM64平台wheel
    │   └── universal/           # 通用wheel包
    ├── models/                   # AI模型文件缓存
    │   ├── torch/               # PyTorch模型文件
    │   ├── funasr/              # FunASR模型文件
    │   └── sherpa/              # Sherpa模型文件
    └── compiled/                # 预编译二进制缓存
        ├── torch-2.2.2-amd64/  # 按版本和架构分类
        └── torchaudio-2.2.2-amd64/

docker-cache-optimization/        # 新增：优化工具目录
├── scripts/                      # 缓存管理脚本
│   ├── cache-manager.sh         # 主缓存管理器
│   ├── wheel-prebuild.sh        # Wheel预构建脚本
│   ├── cache-warm.sh            # 缓存预热脚本
│   ├── cache-clean.sh           # 智能清理脚本
│   └── cross-build-helper.sh    # 跨平台构建助手
├── configs/                      # 配置文件
│   ├── cache-policy.yaml        # 缓存策略配置
│   ├── dependency-groups.yaml   # 依赖分组配置
│   └── build-matrix.yaml        # 构建矩阵配置
├── dockerfiles/                  # 优化后的Dockerfile
│   ├── Dockerfile-server-optimized # 服务器优化版
│   ├── Dockerfile-cache-builder    # 缓存构建器
│   └── Dockerfile-multi-stage     # 多阶段优化版
├── templates/                    # Docker模板文件
│   ├── base-python.Dockerfile   # Python基础镜像模板
│   ├── ml-dependencies.Dockerfile # ML依赖层模板
│   └── cross-platform.Dockerfile # 跨平台构建模板
└── docs/                        # 架构文档
    ├── usage-guide.md           # 使用指南
    ├── cache-strategy.md        # 缓存策略说明
    └── troubleshooting.md       # 故障排除指南
```

### 2. 智能缓存管理策略

#### 2.1 分层缓存策略
```mermaid
graph TD
    A[源代码变更] --> B{依赖类型检测}
    B -->|系统依赖| C[APT缓存层]
    B -->|Python轻量依赖| D[PIP基础缓存层]
    B -->|Python重型依赖| E[ML依赖缓存层]
    B -->|Node.js依赖| F[NPM缓存层]
    
    C --> G[系统基础镜像]
    D --> H[Python运行环境]
    E --> I[ML计算环境]
    F --> J[前端构建环境]
    
    G --> K[最终应用镜像]
    H --> K
    I --> K
    J --> K
```

#### 2.2 跨平台缓存复用机制
```mermaid
graph LR
    A[Mac M1 构建] --> B[检测目标架构]
    B --> C{缓存命中检查}
    C -->|命中| D[复用预编译缓存]
    C -->|未命中| E[启动跨平台构建]
    E --> F[生成AMD64缓存]
    F --> G[存储到分类缓存]
    G --> H[下次构建复用]
    D --> I[快速完成构建]
```

### 3. 核心组件设计

#### 3.1 缓存管理器 (cache-manager.sh)
```bash
# 功能模块
- cache_health_check()     # 缓存健康检查
- dependency_analyzer()    # 依赖变更分析
- cache_invalidation()     # 智能失效策略
- cross_platform_sync()   # 跨平台缓存同步
- space_optimization()    # 空间优化清理
- cache_statistics()      # 缓存统计分析
```

#### 3.2 Wheel预构建器 (wheel-prebuild.sh)
```bash
# 重型依赖预构建策略
HEAVY_DEPS=(
    "torch==2.2.2"
    "torchaudio==2.2.2" 
    "funasr==1.2.3"
    "modelscope==1.23.2"
    "sherpa_onnx==1.11.0"
)

# 按架构预构建wheel
for dep in "${HEAVY_DEPS[@]}"; do
    build_wheel_for_platform "$dep" "linux/amd64"
    build_wheel_for_platform "$dep" "linux/arm64"
done
```

#### 3.3 跨平台构建助手 (cross-build-helper.sh)
```bash
# 构建策略选择
detect_build_context() {
    if [[ "$HOST_ARCH" == "arm64" && "$TARGET_ARCH" == "amd64" ]]; then
        use_cross_platform_cache
    else
        use_native_cache
    fi
}

# 缓存路径映射
map_cache_path() {
    local dep_name=$1
    local target_arch=$2
    echo ".docker_cache/cross-platform/wheels/${target_arch}/${dep_name}"
}
```

### 4. 优化后的Dockerfile设计

#### 4.1 多阶段缓存优化版本
```dockerfile
# Stage 0: 缓存准备阶段
FROM alpine:latest AS cache-prep
COPY docker-cache-optimization/scripts/cache-warm.sh /cache-warm.sh
RUN /cache-warm.sh

# Stage 1: 系统依赖缓存层
FROM python:3.10-slim AS system-deps
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y libopus0 ffmpeg

# Stage 2: 轻量Python依赖层
FROM system-deps AS python-base
COPY requirements-base.txt .
RUN --mount=type=cache,target=/root/.cache/pip,sharing=locked \
    pip install -r requirements-base.txt

# Stage 3: 重型ML依赖层
FROM python-base AS ml-deps
COPY requirements-ml.txt .
RUN --mount=type=cache,target=/root/.cache/pip,sharing=locked \
    --mount=type=cache,target=/.docker_cache/cross-platform/wheels,sharing=locked \
    pip install --find-links /.docker_cache/cross-platform/wheels/linux-amd64 \
                -r requirements-ml.txt

# Stage 4: 应用层
FROM ml-deps AS app
COPY main/xiaozhi-server .
CMD ["python", "app.py"]
```

#### 4.2 分离式依赖管理
```yaml
# requirements分层策略
requirements-base.txt:     # 基础依赖（频繁变更）
  - pyyml, websockets, requests等

requirements-ml.txt:       # ML依赖（稳定，重型）
  - torch, torchaudio, funasr等

requirements-dev.txt:      # 开发依赖（可选）
  - pytest, black, flake8等
```

### 5. 缓存策略配置

#### 5.1 cache-policy.yaml
```yaml
cache_policy:
  # 缓存层配置
  layers:
    system:
      ttl: 7d              # 系统包缓存7天
      size_limit: 1GB
    python_base:
      ttl: 3d              # 基础Python包3天
      size_limit: 500MB
    ml_heavy:
      ttl: 30d             # 重型ML包30天
      size_limit: 5GB
  
  # 跨平台策略
  cross_platform:
    enabled: true
    architectures: ["linux/amd64", "linux/arm64"]
    shared_cache: true
    
  # 清理策略
  cleanup:
    auto_clean: true
    trigger_threshold: 80%  # 磁盘使用率超过80%触发清理
    retention_priority:     # 保留优先级
      - ml_heavy           # 最高优先级保留
      - python_base
      - system
```

#### 5.2 dependency-groups.yaml  
```yaml
dependency_groups:
  heavy_ml:
    packages:
      - torch>=2.0.0
      - torchaudio>=2.0.0
      - funasr>=1.0.0
    cache_strategy: "long_term"
    prebuild: true
    
  base_utils:
    packages:
      - pyyml
      - websockets  
      - requests
    cache_strategy: "medium_term"
    prebuild: false
    
  development:
    packages:
      - pytest
      - black
    cache_strategy: "short_term"
    prebuild: false
```

### 6. 性能优化指标

#### 6.1 构建时间优化目标
- **首次构建**: 从40分钟 → 25分钟 (38%提升)
- **增量构建**: 从15分钟 → 3分钟 (80%提升)  
- **缓存命中构建**: 从3分钟 → 30秒 (83%提升)

#### 6.2 存储空间优化
- **缓存空间利用率**: 从60% → 85%
- **重复数据减少**: 70%减少
- **智能清理**: 自动回收过期缓存

#### 6.3 跨平台构建效率
- **Mac M1 → AMD64**: 缓存命中率从30% → 85%
- **构建一致性**: 100%跨平台构建结果一致
- **缓存共享**: 支持团队成员间缓存共享

### 7. 实施计划

#### 阶段1: 基础架构搭建 (1周)
- [ ] 创建优化目录结构
- [ ] 实现基础缓存管理脚本
- [ ] 设计分层Dockerfile

#### 阶段2: 跨平台缓存优化 (1周)  
- [ ] 实现wheel预构建机制
- [ ] 开发跨平台构建助手
- [ ] 测试缓存复用效果

#### 阶段3: 智能管理功能 (1周)
- [ ] 实现智能清理策略
- [ ] 添加缓存统计分析
- [ ] 完善监控和告警

#### 阶段4: 集成测试与优化 (1周)
- [ ] 完整构建流程测试
- [ ] 性能基准对比
- [ ] 文档完善和培训

### 8. 风险控制

#### 8.1 兼容性风险
- **缓存格式兼容**: 保持与现有.docker_cache结构兼容
- **构建脚本兼容**: 提供fallback机制
- **团队使用**: 渐进式迁移策略

#### 8.2 存储空间风险  
- **空间监控**: 实时监控缓存空间使用
- **自动清理**: 智能清理防止空间耗尽
- **告警机制**: 空间使用率告警

#### 8.3 构建稳定性
- **多重镜像源**: 失败时自动切换镜像源
- **缓存验证**: 缓存完整性校验
- **回退机制**: 缓存失效时的备用方案

## 总结

本方案通过分层缓存、跨平台优化、智能管理等策略，将显著提升Docker构建效率，特别是在Mac M1 → AMD64跨平台构建场景下的Python重型依赖处理能力。同时保持与现有系统的兼容性，支持渐进式部署和团队协作。