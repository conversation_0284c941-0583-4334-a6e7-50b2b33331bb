# Token限制架构修改规范

## 1. 概述

本规范定义了小智ESP32服务器从次数限制改为Token限制的完整架构修改方案，包括数据模型重构、业务逻辑调整和数据迁移策略。

## 2. 数据模型变更规范

### 2.1 QuotaUsageEntity重构

#### 当前问题
- [`usageValue`](main/manager-api/src/main/java/xiaozhi/modules/quota/entity/QuotaUsageEntity.java:48)字段含义不明确
- 缺少Token细分计量(input/output)
- 无法支持精确的Token统计

#### 修改方案

```java
// 新增字段规范
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_quota_usage")
public class QuotaUsageEntity {
    // ... 现有字段保持不变
    
    /**
     * 输入Token数量
     */
    private Integer inputTokens;
    
    /**
     * 输出Token数量  
     */
    private Integer outputTokens;
    
    /**
     * Token总量 (inputTokens + outputTokens)
     * 可作为计算字段或冗余存储
     */
    private Integer totalTokens;
    
    /**
     * @deprecated 使用totalTokens替代
     */
    @Deprecated
    private Integer usageValue;
    
    /**
     * 模型名称 - 用于不同模型Token计费
     */
    private String modelName;
}
```

### 2.2 DeviceEntity关联逻辑调整

#### 当前问题
- [`userId`](main/manager-api/src/main/java/xiaozhi/modules/device/entity/DeviceEntity.java:26)字段建立设备与用户直接关联
- 违反新架构设计原则

#### 修改方案

```java
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_device")
public class DeviceEntity {
    // ... 其他字段保持不变
    
    /**
     * @deprecated 设备不再直接关联用户，通过agentId间接关联
     * 保留字段向后兼容，新逻辑中忽略此字段
     */
    @Deprecated
    @Schema(description = "关联用户ID - 已废弃")
    private Long userId;
    
    /**
     * 智能体ID - 设备唯一关联的智能体
     * 默认绑定到官方智能体
     */
    @Schema(description = "智能体ID")
    private String agentId;
}
```

### 2.3 新增配额累计视图

创建账号级别Token累计的数据视图：

```sql
-- 账号Token使用量累计视图
CREATE VIEW v_account_token_usage AS
SELECT 
    a.user_id,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.user_id IS NOT NULL  -- 排除默认智能体
GROUP BY a.user_id, qu.usage_date;
```

## 3. Token使用量计算算法

### 3.1 Token累计算法伪代码

```pseudocode
ALGORITHM: calculateAccountTokenUsage(userId, date)
INPUT: 
    - userId: 账号ID
    - date: 统计日期
OUTPUT: 
    - totalTokens: 账号当日Token总使用量

BEGIN
    totalTokens = 0
    
    // 获取账号下所有自定义智能体
    customAgents = getCustomAgentsByUserId(userId)
    
    FOR each agent IN customAgents DO
        agentUsage = getAgentTokenUsage(agent.id, date)
        totalTokens += agentUsage.totalTokens
    END FOR
    
    RETURN totalTokens
END

ALGORITHM: getAgentTokenUsage(agentId, date)
INPUT:
    - agentId: 智能体ID
    - date: 统计日期
OUTPUT:
    - tokenUsage: 智能体当日Token使用量

BEGIN
    usage = QUERY "SELECT SUM(input_tokens) as input, 
                          SUM(output_tokens) as output,
                          SUM(total_tokens) as total
                   FROM ai_quota_usage 
                   WHERE agent_id = ? AND usage_date = ?"
                   WITH agentId, date
    
    RETURN usage
END
```

### 3.2 Token累计优化算法

```pseudocode
ALGORITHM: calculateTokenUsageWithCache(userId, date)
INPUT:
    - userId: 账号ID  
    - date: 统计日期
OUTPUT:
    - totalTokens: 缓存优化的Token使用量

BEGIN
    cacheKey = "token_usage:" + userId + ":" + date
    
    // 尝试从缓存获取
    cachedUsage = getFromCache(cacheKey)
    IF cachedUsage IS NOT NULL THEN
        RETURN cachedUsage
    END IF
    
    // 计算实际使用量
    totalTokens = calculateAccountTokenUsage(userId, date)
    
    // 缓存结果(TTL: 1小时)
    setCache(cacheKey, totalTokens, 3600)
    
    RETURN totalTokens
END
```

## 4. 设备绑定流程伪代码

### 4.1 设备初始绑定流程

```pseudocode
ALGORITHM: bindDeviceToDefaultAgent(deviceMac)
INPUT:
    - deviceMac: 设备MAC地址
OUTPUT:
    - success: 绑定结果

BEGIN
    // 获取官方默认智能体
    defaultAgent = getOfficialDefaultAgent()
    IF defaultAgent IS NULL THEN
        defaultAgent = createOfficialDefaultAgent()
    END IF
    
    // 检查设备是否存在
    device = getDeviceByMac(deviceMac)
    IF device IS NULL THEN
        // 创建新设备记录
        device = createDevice(deviceMac, defaultAgent.id)
    ELSE
        // 更新现有设备的智能体绑定
        device.agentId = defaultAgent.id
        device.userId = NULL  // 清除直接用户关联
        updateDevice(device)
    END IF
    
    RETURN SUCCESS
END

ALGORITHM: getOfficialDefaultAgent()
OUTPUT:
    - agent: 官方默认智能体

BEGIN
    agent = QUERY "SELECT * FROM ai_agent 
                   WHERE agent_code IN ('DEFAULT', 'OFFICIAL') 
                   AND user_id IS NULL"
    RETURN agent
END

ALGORITHM: createOfficialDefaultAgent()
OUTPUT:
    - agent: 新创建的官方默认智能体

BEGIN
    agent = new AgentEntity()
    agent.id = generateUUID()
    agent.agentCode = "OFFICIAL"
    agent.name = "官方默认智能体"
    agent.userId = NULL
    agent.isDefault = TRUE
    
    insertAgent(agent)
    RETURN agent
END
```

### 4.2 设备智能体切换流程

```pseudocode
ALGORITHM: switchDeviceAgent(deviceMac, newAgentId, userId)
INPUT:
    - deviceMac: 设备MAC地址
    - newAgentId: 新智能体ID
    - userId: 用户ID(用于权限验证)
OUTPUT:
    - result: 切换结果

BEGIN
    // 验证设备存在性
    device = getDeviceByMac(deviceMac)
    IF device IS NULL THEN
        RETURN ERROR("设备不存在")
    END IF
    
    // 验证新智能体存在性和权限
    newAgent = getAgentById(newAgentId)
    IF newAgent IS NULL THEN
        RETURN ERROR("智能体不存在")
    END IF
    
    // 权限检查
    IF newAgent.userId IS NOT NULL AND newAgent.userId != userId THEN
        RETURN ERROR("无权限绑定此智能体")
    END IF
    
    // 记录切换历史
    logAgentSwitch(deviceMac, device.agentId, newAgentId, userId)
    
    // 执行切换
    device.agentId = newAgentId
    updateDevice(device)
    
    // 清理旧绑定的缓存
    clearDeviceCache(deviceMac)
    
    RETURN SUCCESS("智能体切换成功")
END
```

## 5. 配额检查逻辑伪代码

### 5.1 统一配额检查算法

```pseudocode
ALGORITHM: checkTokenQuotaLimit(deviceMac, inputTokens, outputTokens)
INPUT:
    - deviceMac: 设备MAC地址
    - inputTokens: 预计输入Token数
    - outputTokens: 预计输出Token数
OUTPUT:
    - result: 配额检查结果

BEGIN
    totalTokens = inputTokens + outputTokens
    
    // 获取设备绑定的智能体
    device = getDeviceByMac(deviceMac)
    agent = getAgentById(device.agentId)
    
    IF agent.userId IS NULL THEN
        // 默认智能体：检查设备级配额
        result = checkDeviceQuotaLimit(deviceMac, totalTokens)
    ELSE
        // 自定义智能体：检查账号级配额
        result = checkAccountQuotaLimit(agent.userId, totalTokens)
    END IF
    
    RETURN result
END

ALGORITHM: checkDeviceQuotaLimit(deviceMac, tokens)
INPUT:
    - deviceMac: 设备MAC地址
    - tokens: Token数量
OUTPUT:
    - result: 检查结果

BEGIN
    today = getCurrentDate()
    
    // 获取设备当日使用量
    usedTokens = getDeviceTokenUsage(deviceMac, today)
    
    // 获取设备配额设置
    deviceQuota = getDeviceQuotaSetting(deviceMac)
    IF deviceQuota IS NULL THEN
        deviceQuota = getDefaultDeviceQuota()
    END IF
    
    // 检查是否超限
    IF (usedTokens + tokens) > deviceQuota.dailyLimit THEN
        RETURN QUOTA_EXCEEDED("设备每日Token配额不足")
    END IF
    
    RETURN QUOTA_AVAILABLE
END

ALGORITHM: checkAccountQuotaLimit(userId, tokens)
INPUT:
    - userId: 用户ID
    - tokens: Token数量
OUTPUT:
    - result: 检查结果

BEGIN
    today = getCurrentDate()
    
    // 获取账号当日累计使用量(跨所有自定义智能体)
    usedTokens = calculateAccountTokenUsage(userId, today)
    
    // 获取账号配额设置
    accountQuota = getAccountQuotaSetting(userId)
    IF accountQuota IS NULL THEN
        accountQuota = getDefaultAccountQuota()
    END IF
    
    // 检查是否超限
    IF (usedTokens + tokens) > accountQuota.dailyLimit THEN
        RETURN QUOTA_EXCEEDED("账号每日Token配额不足")
    END IF
    
    RETURN QUOTA_AVAILABLE
END
```

### 5.2 Token使用量记录算法

```pseudocode
ALGORITHM: recordTokenUsage(deviceMac, inputTokens, outputTokens, modelName)
INPUT:
    - deviceMac: 设备MAC地址
    - inputTokens: 实际输入Token数
    - outputTokens: 实际输出Token数
    - modelName: 使用的模型名称

BEGIN
    device = getDeviceByMac(deviceMac)
    agent = getAgentById(device.agentId)
    totalTokens = inputTokens + outputTokens
    
    // 创建使用记录
    usage = new QuotaUsageEntity()
    usage.userId = agent.userId  // 可能为NULL(默认智能体)
    usage.deviceMac = deviceMac
    usage.agentId = agent.id
    usage.inputTokens = inputTokens
    usage.outputTokens = outputTokens
    usage.totalTokens = totalTokens
    usage.modelName = modelName
    usage.usageDate = getCurrentDate()
    usage.createDate = getCurrentDateTime()
    
    // 保存记录
    insertTokenUsage(usage)
    
    // 更新缓存
    IF agent.userId IS NOT NULL THEN
        invalidateAccountUsageCache(agent.userId)
    ELSE
        invalidateDeviceUsageCache(deviceMac)
    END IF
END
```

## 6. 数据库迁移策略

### 6.1 表结构变更DDL

```sql
-- 步骤1: 添加新Token字段
ALTER TABLE ai_quota_usage 
ADD COLUMN input_tokens INT DEFAULT 0 COMMENT '输入Token数量',
ADD COLUMN output_tokens INT DEFAULT 0 COMMENT '输出Token数量',
ADD COLUMN total_tokens INT DEFAULT 0 COMMENT 'Token总量',
ADD COLUMN model_name VARCHAR(100) COMMENT '模型名称';

-- 步骤2: 创建索引优化查询性能
CREATE INDEX idx_quota_usage_agent_date ON ai_quota_usage(agent_id, usage_date);
CREATE INDEX idx_quota_usage_device_date ON ai_quota_usage(device_mac, usage_date);
CREATE INDEX idx_quota_usage_user_date ON ai_quota_usage(user_id, usage_date);

-- 步骤3: 设置默认智能体
INSERT INTO ai_agent (id, agent_code, name, user_id, is_default, create_date)
VALUES ('OFFICIAL_DEFAULT_AGENT', 'OFFICIAL', '官方默认智能体', NULL, 1, NOW())
ON DUPLICATE KEY UPDATE name = '官方默认智能体';

-- 步骤4: 更新设备默认绑定
UPDATE ai_device 
SET agent_id = 'OFFICIAL_DEFAULT_AGENT' 
WHERE agent_id IS NULL OR agent_id = '';
```

### 6.2 数据迁移伪代码

```pseudocode
ALGORITHM: migrateExistingData()
BEGIN
    // 步骤1: 迁移历史使用记录
    migrateHistoricalUsageData()
    
    // 步骤2: 清理设备用户直接关联
    cleanupDeviceUserDirectRelation()
    
    // 步骤3: 验证数据一致性
    validateDataConsistency()
END

ALGORITHM: migrateHistoricalUsageData()
BEGIN
    // 将历史usageValue转换为totalTokens
    EXECUTE "UPDATE ai_quota_usage 
             SET total_tokens = COALESCE(usage_value, 0),
                 input_tokens = COALESCE(usage_value, 0),
                 output_tokens = 0
             WHERE total_tokens IS NULL"
    
    // 根据usageType分配input/output
    EXECUTE "UPDATE ai_quota_usage 
             SET input_tokens = total_tokens, output_tokens = 0
             WHERE usage_type = 'input'"
             
    EXECUTE "UPDATE ai_quota_usage 
             SET input_tokens = 0, output_tokens = total_tokens
             WHERE usage_type = 'output'"
END

ALGORITHM: cleanupDeviceUserDirectRelation()
BEGIN
    // 标记userId字段为废弃，但不删除数据
    // 在应用层忽略此字段
    
    logInfo("设备用户直接关联已在应用层废弃，数据保留用于兼容性")
END
```

## 7. 边缘情况和错误处理

### 7.1 边缘情况处理

```pseudocode
ALGORITHM: handleEdgeCases()
BEGIN
    // 情况1: 智能体被删除但设备仍绑定
    handleOrphanedDevices()
    
    // 情况2: 用户删除但智能体仍存在
    handleOrphanedAgents()
    
    // 情况3: Token使用记录与智能体不匹配
    validateTokenUsageConsistency()
    
    // 情况4: 配额设置缺失
    ensureDefaultQuotaSettings()
END

ALGORITHM: handleOrphanedDevices()
BEGIN
    orphanedDevices = QUERY "SELECT * FROM ai_device d 
                            WHERE d.agent_id NOT IN 
                            (SELECT id FROM ai_agent)"
    
    FOR each device IN orphanedDevices DO
        // 重新绑定到默认智能体
        device.agentId = getOfficialDefaultAgent().id
        updateDevice(device)
        
        logWarning("设备 " + device.macAddress + " 重新绑定到默认智能体")
    END FOR
END

ALGORITHM: handleOrphanedAgents()
BEGIN
    orphanedAgents = QUERY "SELECT * FROM ai_agent a 
                           WHERE a.user_id IS NOT NULL 
                           AND a.user_id NOT IN 
                           (SELECT id FROM sys_user)"
    
    FOR each agent IN orphanedAgents DO
        // 将智能体标记为系统拥有
        agent.userId = NULL
        agent.agentCode = "ORPHANED_" + agent.id
        updateAgent(agent)
        
        logWarning("智能体 " + agent.id + " 用户已删除，转为系统智能体")
    END FOR
END
```

### 7.2 错误处理策略

```pseudocode
ALGORITHM: handleTokenQuotaErrors(error, context)
INPUT:
    - error: 错误信息
    - context: 错误上下文
OUTPUT:
    - response: 错误响应

BEGIN
    SWITCH error.type DO
        CASE QUOTA_EXCEEDED:
            RETURN buildQuotaExceededResponse(context)
            
        CASE AGENT_NOT_FOUND:
            // 自动绑定到默认智能体
            bindDeviceToDefaultAgent(context.deviceMac)
            RETURN RETRY_REQUEST
            
        CASE TOKEN_CALCULATION_ERROR:
            logError("Token计算错误", error, context)
            RETURN DEFAULT_QUOTA_RESPONSE
            
        CASE DATABASE_ERROR:
            logCritical("数据库错误", error)
            RETURN SERVICE_UNAVAILABLE
            
        DEFAULT:
            logError("未知错误", error, context)
            RETURN INTERNAL_ERROR
    END SWITCH
END

ALGORITHM: buildQuotaExceededResponse(context)
BEGIN
    response = new QuotaResponse()
    response.success = FALSE
    response.errorCode = "QUOTA_EXCEEDED"
    response.message = "Token配额已超限"
    
    // 提供额外信息
    IF context.agentType == "DEFAULT" THEN
        response.suggestion = "设备每日Token已用完，请明日再试"
    ELSE
        response.suggestion = "账号Token配额不足，请联系管理员或升级套餐"
    END IF
    
    RETURN response
END
```

## 8. 实施计划和风险控制

### 8.1 分阶段实施计划

```
阶段1: 数据模型准备 (1-2天)
- 添加新Token字段
- 创建数据库索引
- 部署默认智能体

阶段2: 业务逻辑重构 (3-5天)  
- 实现Token计算算法
- 重构配额检查逻辑
- 更新设备绑定流程

阶段3: 数据迁移 (1-2天)
- 迁移历史数据
- 验证数据一致性
- 性能测试

阶段4: 灰度发布 (2-3天)
- 小范围用户测试
- 监控Token使用情况
- 修复发现的问题

阶段5: 全量上线 (1天)
- 全量部署
- 废弃旧逻辑
- 文档更新
```

### 8.2 风险控制措施

```pseudocode
ALGORITHM: implementRiskControls()
BEGIN
    // 数据备份
    createDatabaseBackup("before_token_migration")
    
    // 灰度发布机制
    enableFeatureFlag("token_quota_system", 10%)  // 10%用户
    
    // 监控指标
    setupMonitoring([
        "token_calculation_latency",
        "quota_check_success_rate", 
        "database_connection_pool",
        "cache_hit_rate"
    ])
    
    // 回滚策略
    prepareRollbackPlan([
        "disable_token_quota_feature",
        "restore_old_quota_logic",
        "restore_database_backup"
    ])
END
```

## 9. 性能优化建议

### 9.1 缓存策略

```pseudocode
ALGORITHM: implementCachingStrategy()
BEGIN
    // Token使用量缓存
    cacheAccountUsage(userId, date, TTL=3600)  // 1小时
    cacheDeviceUsage(deviceMac, date, TTL=3600)
    
    // 配额设置缓存
    cacheQuotaSettings(userId, TTL=86400)  // 24小时
    
    // 智能体信息缓存
    cacheAgentInfo(agentId, TTL=3600)
    
    // 设备绑定缓存
    cacheDeviceBinding(deviceMac, TTL=1800)  // 30分钟
END
```

### 9.2 数据库优化

```sql
-- 分区表优化(按日期分区)
CREATE TABLE ai_quota_usage_partitioned (
    -- 所有字段...
) PARTITION BY RANGE (TO_DAYS(usage_date)) (
    PARTITION p_2025_01 VALUES LESS THAN (TO_DAYS('2025-02-01')),
    PARTITION p_2025_02 VALUES LESS THAN (TO_DAYS('2025-03-01')),
    -- 更多分区...
);

-- 复合索引优化
CREATE INDEX idx_quota_complex ON ai_quota_usage(agent_id, usage_date, total_tokens);
```

## 10. 总结

本规范提供了完整的Token限制架构修改方案，主要改进包括：

1. **数据模型重构**: 明确Token计量单位，支持input/output分离统计
2. **关联逻辑优化**: 消除设备-用户直接关联，简化架构设计  
3. **配额累计算法**: 实现账号级Token跨智能体累计计算
4. **设备绑定机制**: 自动绑定默认智能体，支持无缝切换
5. **错误处理完善**: 覆盖各种边缘情况和异常场景

通过分阶段实施和严格的风险控制，可以安全地完成架构升级，满足新的Token限制业务需求。