#!/bin/bash
# docker-cleanup.sh
# Docker清理工具 - 从docker-build-push-optimized.sh中拆分出来的独立清理脚本
# 提供轻量级和完全清理功能

# 终端颜色设置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的信息
info() { echo -e "${GREEN}[INFO] $1${NC}" >&2; }
warn() { echo -e "${YELLOW}[WARN] $1${NC}" >&2; }
error() { echo -e "${RED}[ERROR] $1${NC}" >&2; }

# 脚本标题
echo -e "${BLUE}==========================================${NC}"
echo -e "${BLUE}      小智ESP32服务器 Docker清理工具      ${NC}"
echo -e "${BLUE}==========================================${NC}"

# 默认配置
CLEANUP_MODE="light"
AUTO_CONFIRM=false
DRY_RUN=false
PRESERVE_BASE_IMAGES=true

# 轻量级Docker缓存清理
light_clean_docker_cache() {
    info "执行轻量级Docker缓存清理 (docker builder prune)..."
    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] 将执行: docker builder prune -f"
        return 0
    fi
    
    if ! docker builder prune -f; then
        warn "docker builder prune -f failed. May indicate no active buildkit builder or no cache to prune."
    fi
}

# 完全Docker缓存清理
full_clean_docker_cache() {
    info "执行智能Docker缓存清理（保留基础镜像）..."

    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] 将执行完全清理操作"
        return 0
    fi

    # 获取所有基础镜像名称
    local base_images=""
    if [[ "$PRESERVE_BASE_IMAGES" == "true" ]]; then
        base_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E ".*-base:(amd64|arm64)$|.*-web-.*-base:(amd64|arm64)$" || true)

        if [[ -n "$base_images" ]]; then
            info "检测到以下基础镜像，将予以保留："
            echo "$base_images" | sed 's/^/  - /'
        fi
    fi

    # 删除悬空镜像
    info "删除悬空镜像..."
    docker image prune -f

    # 删除未使用的容器
    info "删除未使用的容器..."
    docker container prune -f

    # 删除未使用的网络
    info "删除未使用的网络..."
    docker network prune -f

    # 删除未使用的卷
    info "删除未使用的卷..."
    docker volume prune -f

    # 清理构建缓存（保留最近使用的）
    info "清理构建缓存（保留最近7天使用的）..."
    docker builder prune -f --filter "until=168h" --keep-storage=5GB || true

    info "智能Docker缓存清理完成（基础镜像已保留）"
}

# 删除指定镜像
remove_image() {
    local image_name="$1"
    
    if [[ -z "$image_name" ]]; then
        error "镜像名称不能为空"
        return 1
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] 将删除镜像: $image_name"
        return 0
    fi
    
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image_name$"; then
        info "删除镜像: $image_name"
        docker rmi "$image_name" 2>/dev/null || warn "删除镜像失败: $image_name"
    else
        warn "镜像不存在: $image_name"
    fi
}

# 删除所有xiaozhi相关镜像（除基础镜像外）
clean_xiaozhi_images() {
    info "清理所有xiaozhi相关镜像..."
    
    local xiaozhi_images
    if [[ "$PRESERVE_BASE_IMAGES" == "true" ]]; then
        xiaozhi_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "xiaozhi" | grep -v -E ".*-base:(amd64|arm64)$|.*-web-.*-base:(amd64|arm64)$" || true)
    else
        xiaozhi_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "xiaozhi" || true)
    fi
    
    if [[ -z "$xiaozhi_images" ]]; then
        info "未找到xiaozhi相关镜像"
        return 0
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] 将删除以下镜像："
        echo "$xiaozhi_images" | sed 's/^/  - /'
        return 0
    fi
    
    info "找到以下xiaozhi相关镜像："
    echo "$xiaozhi_images" | sed 's/^/  - /'
    
    if [[ "$AUTO_CONFIRM" != "true" ]]; then
        read -p "是否删除这些镜像? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "操作已取消"
            return 0
        fi
    fi
    
    echo "$xiaozhi_images" | while read -r image; do
        if [[ -n "$image" ]]; then
            remove_image "$image"
        fi
    done
}

# 系统清理（最彻底的清理）
system_clean() {
    warn "执行系统级清理（将删除所有未使用的Docker资源）..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        info "[DRY RUN] 将执行: docker system prune -a -f"
        return 0
    fi
    
    if [[ "$AUTO_CONFIRM" != "true" ]]; then
        echo "警告：此操作将删除："
        echo "  - 所有停止的容器"
        echo "  - 所有未使用的网络"
        echo "  - 所有未使用的镜像（不仅仅是悬空镜像）"
        echo "  - 所有构建缓存"
        read -p "确定要继续吗? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            info "操作已取消"
            return 0
        fi
    fi
    
    docker system prune -a -f
    info "系统级清理完成"
}

# 显示帮助信息
show_help() {
    echo "Docker清理工具 - 小智ESP32服务器项目"
    echo ""
    echo "用法: $0 [选项] [清理模式]"
    echo ""
    echo "清理模式:"
    echo "  light              轻量级清理（默认）- 仅清理构建缓存"
    echo "  full               完全清理 - 清理缓存、悬空镜像、未使用容器等"
    echo "  xiaozhi            清理xiaozhi相关镜像"
    echo "  system             系统级清理 - 最彻底的清理（慎用）"
    echo "  image <镜像名>     删除指定镜像"
    echo ""
    echo "选项:"
    echo "  -h, --help         显示帮助信息"
    echo "  -y, --yes          自动确认所有提示"
    echo "  --dry-run          预览模式，显示将要执行的操作但不实际执行"
    echo "  --no-preserve-base 不保留基础镜像（默认会保留 *-base:amd64 和 *-base:arm64）"
    echo ""
    echo "示例:"
    echo "  $0                 # 执行轻量级清理"
    echo "  $0 full            # 执行完全清理"
    echo "  $0 xiaozhi -y      # 自动清理所有xiaozhi镜像"
    echo "  $0 --dry-run full  # 预览完全清理操作"
    echo "  $0 image xiaozhi-esp32-server:latest  # 删除指定镜像"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -y|--yes)
            AUTO_CONFIRM=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --no-preserve-base)
            PRESERVE_BASE_IMAGES=false
            shift
            ;;
        light|full|xiaozhi|system)
            CLEANUP_MODE="$1"
            shift
            ;;
        image)
            CLEANUP_MODE="image"
            if [[ -n "$2" && "$2" != -* ]]; then
                IMAGE_NAME="$2"
                shift 2
            else
                error "image模式需要指定镜像名称"
                exit 1
            fi
            ;;
        *)
            error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证Docker是否安装
if ! command -v docker &> /dev/null; then
    error "Docker未安装，请先安装Docker!"
    exit 1
fi

# 显示当前配置
info "Docker清理配置:"
echo "-----------------------------------"
echo "清理模式: $CLEANUP_MODE"
echo "自动确认: $AUTO_CONFIRM"
echo "预览模式: $DRY_RUN"
echo "保留基础镜像: $PRESERVE_BASE_IMAGES"
if [[ "$CLEANUP_MODE" == "image" ]]; then
    echo "目标镜像: $IMAGE_NAME"
fi
echo "-----------------------------------"

# 执行清理操作
case $CLEANUP_MODE in
    light)
        light_clean_docker_cache
        ;;
    full)
        full_clean_docker_cache
        ;;
    xiaozhi)
        clean_xiaozhi_images
        ;;
    system)
        system_clean
        ;;
    image)
        remove_image "$IMAGE_NAME"
        ;;
    *)
        error "未知的清理模式: $CLEANUP_MODE"
        show_help
        exit 1
        ;;
esac

info "清理操作完成！"

# 显示清理后的状态
if [[ "$DRY_RUN" != "true" ]]; then
    echo ""
    info "当前Docker资源使用情况:"
    echo "镜像数量: $(docker images -q | wc -l)"
    echo "容器数量: $(docker ps -a -q | wc -l)"
    echo "卷数量: $(docker volume ls -q | wc -l)"
    echo "网络数量: $(docker network ls -q | wc -l)"
fi
