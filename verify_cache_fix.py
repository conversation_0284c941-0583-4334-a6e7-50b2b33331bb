#!/usr/bin/env python3
"""
验证缓存一致性修复的脚本
模拟设备首次连接 → 自动注册 → 再次连接的完整流程
"""

import requests
import json
import time
import sys
import os

def test_cache_consistency(mac_address, api_base_url, api_key):
    """测试缓存一致性修复"""
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"🧪 测试缓存一致性修复")
    print(f"📱 MAC地址: {mac_address}")
    print(f"📡 API地址: {api_base_url}")
    print("=" * 80)
    
    # 步骤1：清理现有缓存和数据（模拟全新设备）
    print("1️⃣ 清理现有缓存和数据...")
    
    # 清理缓存
    invalidate_url = f"{api_base_url}/device/auth/invalidate/{mac_address}"
    try:
        response = requests.get(invalidate_url, headers=headers, timeout=10)
        print(f"   缓存清理: {response.status_code}")
    except Exception as e:
        print(f"   缓存清理异常: {e}")
    
    # 删除设备记录（如果存在）
    delete_url = f"{api_base_url}/device/mac/{mac_address}"
    try:
        response = requests.delete(delete_url, headers=headers, timeout=10)
        print(f"   设备删除: {response.status_code}")
    except Exception as e:
        print(f"   设备删除异常: {e}")
    
    print()
    
    # 步骤2：首次认证（应该失败并缓存失败结果）
    print("2️⃣ 首次认证（预期失败）...")
    check_url = f"{api_base_url}/device/auth/check/{mac_address}"
    try:
        response = requests.get(check_url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                is_enabled = data.get("data", False)
                print(f"   ✅ 首次认证结果: {'启用' if is_enabled else '未启用'} (预期: 未启用)")
                if not is_enabled:
                    print("   📝 缓存了'未启用'状态，符合预期")
                else:
                    print("   ⚠️  意外：设备显示为启用状态")
            else:
                print(f"   ❌ 认证检查失败: {data.get('msg')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print()
    
    # 步骤3：执行自动注册
    print("3️⃣ 执行自动注册...")
    auth_url = f"{api_base_url}/device/auth/authenticate"
    auth_data = {
        "macAddress": mac_address,
        "clientId": "test_auto_register"
    }
    try:
        response = requests.post(auth_url, headers=headers, json=auth_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                result = data.get("data", {})
                authenticated = result.get("authenticated", False)
                method = result.get("method", "unknown")
                print(f"   ✅ 自动注册结果: {'成功' if authenticated else '失败'}")
                print(f"   📋 认证方法: {method}")
                if method == "auto_register":
                    print("   🎉 自动注册成功！")
                else:
                    print("   ⚠️  认证方法不是auto_register")
            else:
                print(f"   ❌ 自动注册失败: {data.get('msg')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print()
    
    # 步骤4：等待一下，然后再次认证（测试缓存是否已清理）
    print("4️⃣ 等待2秒后再次认证（测试缓存清理）...")
    time.sleep(2)
    
    try:
        response = requests.get(check_url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                is_enabled = data.get("data", False)
                print(f"   ✅ 再次认证结果: {'启用' if is_enabled else '未启用'} (预期: 启用)")
                if is_enabled:
                    print("   🎉 缓存一致性修复成功！设备现在显示为启用状态")
                else:
                    print("   ❌ 缓存一致性问题仍然存在！设备仍显示为未启用")
            else:
                print(f"   ❌ 认证检查失败: {data.get('msg')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    print()
    
    # 步骤5：验证数据库状态
    print("5️⃣ 验证数据库状态...")
    mapping_url = f"{api_base_url}/device/mapping/{mac_address}"
    try:
        response = requests.get(mapping_url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                mapping = data.get("data", {})
                print(f"   ✅ 设备映射: MAC={mapping.get('macAddress')}, Agent={mapping.get('agentId')}")
                if mapping.get('agentId') == 'OFFICIAL_DEFAULT_AGENT':
                    print("   📝 设备已正确绑定到官方默认智能体")
                else:
                    print(f"   ⚠️  设备绑定到了其他智能体: {mapping.get('agentId')}")
            else:
                print(f"   ❌ 获取设备映射失败: {data.get('msg')}")
        else:
            print(f"   ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")

def main():
    # 配置信息
    MAC_ADDRESS = "94:a9:90:29:06:10"  # 测试MAC地址
    API_BASE_URL = "http://**************:8002"  # 测试服务器API地址
    
    # 从环境变量获取API密钥
    API_KEY = os.getenv("XIAOZHI_API_KEY")
    
    if not API_KEY:
        print("❌ 请设置环境变量 XIAOZHI_API_KEY")
        print("💡 export XIAOZHI_API_KEY=your_actual_api_key")
        sys.exit(1)
    
    test_cache_consistency(MAC_ADDRESS, API_BASE_URL, API_KEY)
    
    print("\n🎯 测试完成！")
    print("\n📋 结果分析:")
    print("✅ 如果再次认证显示'启用'，说明缓存一致性修复成功")
    print("❌ 如果再次认证仍显示'未启用'，说明缓存问题仍然存在")
    print("\n💡 注意事项:")
    print("1. 确保manager-api服务已重启（应用代码修复）")
    print("2. 确保MAC认证和自动注册都已启用")
    print("3. 如果测试失败，检查manager-api日志中的缓存清理记录")

if __name__ == "__main__":
    main()
