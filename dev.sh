#!/bin/bash
# 小智ESP32服务器开发环境管理脚本（简化版）
# 用于调用dev-tools目录中的脚本

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
DEV_TOOLS_DIR="$SCRIPT_DIR/dev-tools"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查dev-tools目录是否存在
if [ ! -d "$DEV_TOOLS_DIR" ]; then
    echo -e "${RED}错误: dev-tools目录不存在${NC}"
    exit 1
fi

# 检查dev-env.sh脚本是否存在
if [ ! -f "$DEV_TOOLS_DIR/dev-env.sh" ]; then
    echo -e "${RED}错误: dev-env.sh脚本不存在${NC}"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$DEV_TOOLS_DIR/dev-env.sh"
chmod +x "$DEV_TOOLS_DIR/test-docker-context.sh"
chmod +x "$DEV_TOOLS_DIR/test_mirrors.sh"

# 显示帮助信息
show_help() {
    echo -e "${GREEN}小智ESP32服务器开发环境管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start       启动开发环境容器"
    echo "  stop        停止开发环境容器"
    echo "  restart     重启开发环境容器"
    echo "  status      显示开发环境容器状态"
    echo "  logs        查看容器日志"
    echo "  build       构建开发环境镜像"
    echo "  rebuild     重新构建开发环境镜像（不使用缓存）"
    echo "  shell       进入服务器容器的Shell"
    echo "  web-shell   进入Web容器的Shell"
    echo "  clean       清理无用的Docker资源"
    echo "  test-context 测试Docker构建上下文"
    echo "  test-mirrors 测试不同Debian镜像源的速度"
    echo "  help        显示此帮助信息"
    echo ""
}

# 根据命令执行相应的操作
case "$1" in
    start|stop|restart|status|logs|build|rebuild|shell|web-shell|clean)
        # 调用dev-env.sh脚本
        "$DEV_TOOLS_DIR/dev-env.sh" "$@"
        ;;
    test-context)
        # 调用test-docker-context.sh脚本
        "$DEV_TOOLS_DIR/test-docker-context.sh"
        ;;
    test-mirrors)
        # 调用test_mirrors.sh脚本
        "$DEV_TOOLS_DIR/test_mirrors.sh"
        ;;
    help|*)
        show_help
        ;;
esac