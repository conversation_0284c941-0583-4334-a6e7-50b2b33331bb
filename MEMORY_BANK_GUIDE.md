# 📚 项目知识库指南

## 🎯 快速导航

**xiaozhi-esp32-server** 项目的所有核心技术文档已整合到 **`memory-bank`** 目录中，提供统一、准确、基于实际代码的技术文档。

## 📖 主要文档

### 🏗️ [项目架构总览](./memory-bank/PROJECT_ARCHITECTURE.md)
- 三层架构设计详解
- 核心功能模块介绍
- 技术栈和部署模式
- 开发扩展能力

### 🔐 [认证系统详解](./memory-bank/AUTHENTICATION_SYSTEM.md)
- 二选一认证机制 (MAC vs Token)
- 认证流程和安全特性
- 配置示例和最佳实践

### 🔧 [Provider系统详解](./memory-bank/PROVIDER_SYSTEM.md)
- AI服务抽象基类设计
- ASR/LLM/TTS具体实现
- 扩展新Provider指南

### 🔌 [插件系统详解](./memory-bank/PLUGIN_SYSTEM.md)
- 装饰器注册机制
- 函数类型和调用流程
- 自定义插件开发

### 🗄️ [数据库架构详解](./memory-bank/DATABASE_SCHEMA.md)
- 核心表结构设计
- 数据关系和索引策略
- 性能优化建议

### 🚀 [部署指南](./memory-bank/DEPLOYMENT_GUIDE.md)
- Docker和源码部署
- 配置管理和监控
- 故障排除指南

## 🎯 角色导航

### 👨‍💻 开发者
1. [项目架构总览](./memory-bank/PROJECT_ARCHITECTURE.md) - 了解整体设计
2. [Provider系统详解](./memory-bank/PROVIDER_SYSTEM.md) - 学习AI服务集成
3. [插件系统详解](./memory-bank/PLUGIN_SYSTEM.md) - 开发自定义功能

### 🔧 运维工程师
1. [部署指南](./memory-bank/DEPLOYMENT_GUIDE.md) - 部署和运维
2. [认证系统详解](./memory-bank/AUTHENTICATION_SYSTEM.md) - 安全配置
3. [数据库架构详解](./memory-bank/DATABASE_SCHEMA.md) - 数据管理

### 🏗️ 架构师
1. [项目架构总览](./memory-bank/PROJECT_ARCHITECTURE.md) - 整体架构
2. [数据库架构详解](./memory-bank/DATABASE_SCHEMA.md) - 数据设计
3. [认证系统详解](./memory-bank/AUTHENTICATION_SYSTEM.md) - 安全架构

## 📁 其他文档目录

### docs/ - 特定功能指南
- 部署文档 (Deployment.md, Deployment_all.md)
- 集成指南 (homeassistant-integration.md, fish-speech-integration.md)
- 开发运维 (dev-ops-integration.md)
- FAQ和贡献指南

### analysis-docs/ - 已迁移
- 原分析文档已迁移到memory-bank
- 查看 [analysis-docs/README.md](./analysis-docs/README.md) 了解迁移详情

## ✨ memory-bank的优势

### 🎯 基于代码分析
- 所有文档基于实际代码分析
- 确保信息的准确性和时效性
- 避免文档与代码不一致

### 📚 统一组织
- 集中管理所有核心文档
- 统一的格式和结构
- 完整的索引和导航

### 🔄 持续更新
- 跟随代码变更及时更新
- 定期检查文档时效性
- 社区贡献和反馈机制

## ⚠️ 重要规则：文档同步更新

**每次进行重要的逻辑修改后，必须同步更新memory-bank的相关文档**

这是一个**必备规则**，确保文档与代码保持一致：
- 🔧 架构变更 → 更新 [PROJECT_ARCHITECTURE.md](./memory-bank/PROJECT_ARCHITECTURE.md)
- 🔐 认证逻辑 → 更新 [AUTHENTICATION_SYSTEM.md](./memory-bank/AUTHENTICATION_SYSTEM.md)
- 🔌 Provider变更 → 更新 [PROVIDER_SYSTEM.md](./memory-bank/PROVIDER_SYSTEM.md)
- 🔌 插件功能 → 更新 [PLUGIN_SYSTEM.md](./memory-bank/PLUGIN_SYSTEM.md)
- 🗄️ 数据库变更 → 更新 [DATABASE_SCHEMA.md](./memory-bank/DATABASE_SCHEMA.md)
- 🚀 部署配置 → 更新 [DEPLOYMENT_GUIDE.md](./memory-bank/DEPLOYMENT_GUIDE.md)

## 🤝 如何贡献

### 文档更新
1. 发现错误或过时信息 → 提交Issue
2. 新增功能 → 立即更新对应文档（必备规则）
3. 改进建议 → 提交PR

### 代码贡献
1. 遵循现有架构模式
2. 添加适当测试用例
3. **必须同步更新相关文档**

## 📞 获取帮助

- **GitHub Issues**: 报告问题和功能请求
- **文档反馈**: 在memory-bank相关Issue中反馈
- **技术讨论**: 参与GitHub Discussions

---

> 💡 **建议**: 将 `memory-bank` 目录加入书签，这是了解项目的最佳起点！
> ⚠️ **重要**: 每次重要逻辑修改后，必须同步更新memory-bank文档！
> 📅 **最后更新**: 2025-06-09
