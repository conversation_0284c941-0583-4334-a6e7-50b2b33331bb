#!/bin/bash

# 从上游仓库拉取代码并合并到当前仓库的脚本
# 使用方法: ./sync-upstream-code.sh [上游仓库URL] [上游分支]
# 默认上游仓库为 https://github.com/xinnan-tech/xiaozhi-esp32-server.git
# 默认上游分支为 main

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_upstream() {
    echo -e "${PURPLE}[上游]${NC} $1"
}

log_local() {
    echo -e "${CYAN}[本地]${NC} $1"
}

# 检查是否安装了Beyond Compare
check_beyond_compare() {
    if [ ! -d "/Applications/Beyond Compare.app" ]; then
        log_error "未找到Beyond Compare应用程序，请确保已安装"
        log_info "可以从 https://www.scootersoftware.com/download.php 下载安装"
        exit 1
    fi
}

# 获取参数
UPSTREAM_REPO=${1:-https://github.com/xinnan-tech/xiaozhi-esp32-server.git}
UPSTREAM_BRANCH=${2:-main}

# 检查当前是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    log_error "当前有未提交的更改，请先提交或存储这些更改"
    exit 1
fi

# 检查是否安装了Beyond Compare
check_beyond_compare

# 设置上游代码目录（与项目同级）
UPSTREAM_DIR="../upstream-xiaozhi-esp32-server"
log_info "使用与项目同级的目录: $UPSTREAM_DIR"

# 确保目录存在
mkdir -p "$UPSTREAM_DIR"

# 克隆或更新上游仓库
log_step "克隆或更新上游仓库"
if [ -d "$UPSTREAM_DIR/.git" ]; then
    # 如果目录已存在，执行 pull 更新
    log_info "上游仓库目录已存在，执行更新"
    cd "$UPSTREAM_DIR"
    git fetch origin
    git checkout "$UPSTREAM_BRANCH"
    git pull origin "$UPSTREAM_BRANCH"
else
    # 如果目录不存在，执行完整克隆
    log_info "上游仓库目录不存在，执行克隆"
    git clone --branch "$UPSTREAM_BRANCH" "$UPSTREAM_REPO" "$UPSTREAM_DIR"
    cd "$UPSTREAM_DIR"
fi
UPSTREAM_COMMIT=$(git rev-parse HEAD)
log_info "上游仓库最新提交: $UPSTREAM_COMMIT"

# 返回到原始目录
cd - > /dev/null

# 使用Beyond Compare打开目录比较
log_step "使用Beyond Compare比较本地和上游代码"
log_info "正在启动Beyond Compare..."

# 获取当前目录的绝对路径
CURRENT_DIR=$(pwd)

# 启动Beyond Compare进行文件夹比较（使用固定路径）
open -a "/Applications/Beyond Compare.app"

# 输出提示信息
log_info "已启动Beyond Compare，请在其中手动设置过滤器排除以下内容:"
log_info "1. .git 目录"
log_info "2. node_modules 目录"
log_info "3. __pycache__ 目录"
log_info "4. 自定义文件列表中的文件"

# 完成同步
log_step "同步完成"
log_info "请在Beyond Compare中手动同步所需的文件"
log_info "同步完成后，请自行提交更改"
log_info "当前上游提交: $UPSTREAM_COMMIT"

exit 0
