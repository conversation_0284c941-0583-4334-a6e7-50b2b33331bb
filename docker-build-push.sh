#!/bin/bash
# docker-build-push.sh
# 用于构建小智ESP32服务器的Docker镜像并推送到镜像仓库
# 仅执行构建和推送操作，不进行后续部署或其他操作
# 优化版：失败后重新执行不会清空缓存，只有成功后才清空

# 加载环境变量
ENV_FILE=".env"
ENV_TEMPLATE=".env.template"

if [ -f "$ENV_FILE" ]; then
  source "$ENV_FILE"
else
  echo "错误: 环境配置文件 $ENV_FILE 不存在"
  echo "请复制 $ENV_TEMPLATE 为 $ENV_FILE 并填写您的配置"
  exit 1
fi

# 终端颜色设置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

# 脚本标题
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}  小智ESP32服务器 Docker 构建与推送脚本  ${NC}"
echo -e "${BLUE}=========================================${NC}"

# 配置变量
DOCKER_REGISTRY="${DOCKER_REGISTRY:-xiaozhi-esp32-server}"  # Docker镜像仓库地址（本地默认仓库）
GITHUB_REGISTRY="ghcr.io/matatalab"                        # GitHub Container Registry 地址
DOCKERHUB_REGISTRY="docker.io"                             # DockerHub Registry 地址
IMAGE_PREFIX="${IMAGE_PREFIX:-xiaozhi-esp32-server}"       # 镜像名称前缀
IMAGE_TAG="${IMAGE_TAG:-latest}"                           # 镜像标签，默认为latest
BUILD_SERVER="${BUILD_SERVER:-false}"                       # 是否构建服务器镜像
BUILD_WEB="${BUILD_WEB:-false}"                             # 是否构建Web界面镜像
BUILD_ALL="${BUILD_ALL:-true}"                             # 是否构建所有镜像
PUSH_IMAGES="${PUSH_IMAGES:-true}"                         # 是否推送镜像
PUSH_PERSONAL="${PUSH_PERSONAL:-true}"                     # 是否推送到个人服务器
PUSH_GITHUB="${PUSH_GITHUB:-false}"                        # 是否推送到GitHub镜像仓库
PUSH_DOCKERHUB="${PUSH_DOCKERHUB:-false}"                  # 是否推送到DockerHub
SKIP_PRE_CLEAN="${SKIP_PRE_CLEAN:-false}"                  # 是否跳过构建前的清理（失败重试时有用）

AUTO_CONFIRM="${AUTO_CONFIRM:-false}"                      # 是否自动确认所有提示

# 启用BuildKit以获得更好的构建性能和缓存功能
export DOCKER_BUILDKIT=1

# 个人服务器配置
# 使用普通数组而不是关联数组，以兼容更多的bash版本
PERSONAL_SERVER_IDS=("prod" "test")
PERSONAL_SERVER_PROD="正式环境:${PROD_HOST}:${PROD_CERT_PATH}"
PERSONAL_SERVER_TEST="测试环境:${TEST_HOST}:${TEST_CERT_PATH}"
SELECTED_PERSONAL_SERVERS=()                                       # 选择的个人服务器列表

# 显示帮助信息
show_help() {
  echo "小智ESP32服务器Docker镜像构建与推送工具"
  echo ""
  echo "用法: $0 [选项]"
  echo ""
  echo "选项:"
  echo "  -h, --help                显示帮助信息"
  echo "  -r, --registry <地址>      设置Docker镜像仓库地址"
  echo "  -p, --prefix <前缀>        设置镜像名称前缀"
  echo "  -t, --tag <标签>           设置镜像标签"
  echo "  --all                     构建所有镜像（默认选项）"
  echo "  --server-only             仅构建服务器镜像"
  echo "  --web-only                仅构建Web界面镜像"
  echo "  --no-push                 构建镜像但不推送到仓库"
  echo "  --push-personal           交互式选择并推送到个人服务器"
  echo "  --push-github             推送到GitHub Container Registry"
  echo "  --push-dockerhub          推送到DockerHub, 使用 --dockerhub-user 指定用户名"
  echo "  --dockerhub-user <用户名>  设置推送到DockerHub的用户名（与--push-dockerhub一起使用）"
  echo "  --skip-pre-clean          跳过构建前的清理（失败重试时有用）"
  echo "  -y, --yes                 自动确认所有提示，不进行交互"
  echo ""
  echo "示例:"
  echo "  $0 --tag v1.0"
  echo "  $0 --server-only --push-personal"
  echo "  $0 --push-github --push-dockerhub --dockerhub-user myusername"
  echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -r|--registry)
      DOCKER_REGISTRY="$2"
      shift 2
      ;;
    -p|--prefix)
      IMAGE_PREFIX="$2"
      shift 2
      ;;
    -t|--tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --server-only)
      BUILD_SERVER=true
      BUILD_WEB=false
      BUILD_ALL=false
      shift
      ;;
    --web-only)
      BUILD_SERVER=false
      BUILD_WEB=true
      BUILD_ALL=false
      shift
      ;;
    --all)
      BUILD_ALL=true
      BUILD_SERVER=false
      BUILD_WEB=false
      shift
      ;;
    --no-push)
      PUSH_IMAGES=false
      # 同时禁用所有推送相关选项
      PUSH_PERSONAL=false
      PUSH_GITHUB=false
      PUSH_DOCKERHUB=false
      shift
      ;;
    --push-personal)
      PUSH_PERSONAL=true
      PUSH_IMAGES=true  # 确保推送总开关打开
      shift
      ;;
    --push-github)
      PUSH_GITHUB=true
      PUSH_IMAGES=true  # 确保推送总开关打开
      shift
      ;;
    --push-dockerhub)
      PUSH_DOCKERHUB=true
      PUSH_IMAGES=true  # 确保推送总开关打开
      shift
      ;;
    --dockerhub-user)
      DOCKERHUB_USERNAME="$2"
      shift 2
      ;;
    --skip-pre-clean)
      SKIP_PRE_CLEAN=true
      shift
      ;;

    -y|--yes)
      AUTO_CONFIRM=true
      shift
      ;;
    *)
      error "未知参数: $1"
      show_help
      exit 1
      ;;
  esac
done

# 验证Docker是否安装
if ! command -v docker &> /dev/null; then
  error "Docker未安装，请先安装Docker!"
  exit 1
fi

# 如果使用DockerHub，验证是否有用户名设置
if [ "$PUSH_DOCKERHUB" = true ] && [ -z "$DOCKERHUB_USERNAME" ]; then
  error "使用--push-dockerhub选项时必须指定--dockerhub-user参数"
  exit 1
fi

# 交互式选择个人服务器
select_personal_servers() {
  if [ ${#PERSONAL_SERVER_IDS[@]} -eq 0 ]; then
    error "未配置任何个人服务器"
    return 1
  fi

  info "请选择要推送的服务器环境:"
  echo "-----------------------------------"
  echo "1) 全部环境"
  echo "2) 正式环境 (${PROD_HOST})"
  echo "3) 测试环境 (${TEST_HOST})"
  echo "-----------------------------------"

  local selection
  read -p "请输入选项编号 [3]: " selection
  selection=${selection:-3}

  case $selection in
    1)
      # 选择全部服务器
      for server_id in "${PERSONAL_SERVER_IDS[@]}"; do
        SELECTED_PERSONAL_SERVERS+=("$server_id")
      done
      info "已选择: 全部环境"
      ;;
    2)
      # 选择正式环境
      SELECTED_PERSONAL_SERVERS=("prod")
      info "已选择: 正式环境 (${PROD_HOST})"
      ;;
    3)
      # 选择测试环境
      SELECTED_PERSONAL_SERVERS=("test")
      info "已选择: 测试环境 (${TEST_HOST})"
      ;;
    *)
      error "无效的选项编号，使用默认选项：测试环境"
      SELECTED_PERSONAL_SERVERS=("test")
      info "已选择: 测试环境 (${TEST_HOST})"
      ;;
  esac

  return 0
}

# 交互式选择构建镜像类型
select_build_type() {
  info "请选择要构建的镜像类型:"
  echo "-----------------------------------"
  echo "1) 构建所有镜像 (服务器+Web界面)"
  echo "2) 仅构建服务器镜像"
  echo "3) 仅构建Web界面镜像"
  echo "-----------------------------------"

  local selection
  read -p "请输入选项编号 [1]: " selection
  selection=${selection:-1}

  case $selection in
    1)
      BUILD_ALL=true
      BUILD_SERVER=false
      BUILD_WEB=false
      info "已选择: 构建所有镜像"
      ;;
    2)
      BUILD_ALL=false
      BUILD_SERVER=true
      BUILD_WEB=false
      info "已选择: 仅构建服务器镜像"
      ;;
    3)
      BUILD_ALL=false
      BUILD_SERVER=false
      BUILD_WEB=true
      info "已选择: 仅构建Web界面镜像"
      ;;
    *)
      error "无效的选项编号，使用默认选项：构建所有镜像"
      BUILD_ALL=true
      BUILD_SERVER=false
      BUILD_WEB=false
      info "已选择: 构建所有镜像"
      ;;
  esac

  return 0
}

# 交互式选择是否跳过构建前清理
select_clean_option() {
  info "请选择清理选项:"
  echo "-----------------------------------"
  echo "1) 正常清理 (构建前执行轻量级清理，成功后执行完全清理)"
  echo "2) 跳过构建前清理 (适用于重试失败的构建)"
  echo "-----------------------------------"

  local selection
  read -p "请输入选项编号 [1]: " selection
  selection=${selection:-1}

  case $selection in
    1)
      SKIP_PRE_CLEAN=false
      info "已选择: 正常清理"
      ;;
    2)
      SKIP_PRE_CLEAN=true
      info "已选择: 跳过构建前清理"
      ;;
    *)
      error "无效的选项编号，使用默认选项：正常清理"
      SKIP_PRE_CLEAN=false
      info "已选择: 正常清理"
      ;;
  esac

  return 0
}



# 交互式选择环境
select_environment() {
  info "请选择要推送的环境:"
  echo "-----------------------------------"
  echo "1) 仅构建镜像，不推送"
  echo "2) 推送到个人服务器"
  echo "3) 推送到GitHub Container Registry"
  echo "4) 推送到DockerHub (需要用户名)"
  echo "5) 推送到所有环境 (个人服务器+GitHub+DockerHub)"
  echo "-----------------------------------"

  local selection
  read -p "请输入选项编号 [2]: " selection
  selection=${selection:-2}

  case $selection in
    1)
      PUSH_IMAGES=false
      PUSH_PERSONAL=false
      PUSH_GITHUB=false
      PUSH_DOCKERHUB=false
      info "已选择: 仅构建镜像，不推送"
      ;;
    2)
      PUSH_IMAGES=true
      PUSH_PERSONAL=true
      PUSH_GITHUB=false
      PUSH_DOCKERHUB=false
      info "已选择: 推送到个人服务器"
      select_personal_servers || PUSH_PERSONAL=false
      ;;
    3)
      PUSH_IMAGES=true
      PUSH_PERSONAL=false
      PUSH_GITHUB=true
      PUSH_DOCKERHUB=false
      info "已选择: 推送到GitHub Container Registry"
      ;;
    4)
      PUSH_IMAGES=true
      PUSH_PERSONAL=false
      PUSH_GITHUB=false
      PUSH_DOCKERHUB=true
      info "已选择: 推送到DockerHub"
      if [ -z "$DOCKERHUB_USERNAME" ]; then
        read -p "请输入DockerHub用户名: " DOCKERHUB_USERNAME
        if [ -z "$DOCKERHUB_USERNAME" ]; then
          error "DockerHub用户名不能为空"
          PUSH_DOCKERHUB=false
        fi
      fi
      ;;
    5)
      PUSH_IMAGES=true
      PUSH_PERSONAL=true
      PUSH_GITHUB=true
      PUSH_DOCKERHUB=true
      info "已选择: 推送到所有环境"
      select_personal_servers || PUSH_PERSONAL=false
      if [ -z "$DOCKERHUB_USERNAME" ]; then
        read -p "请输入DockerHub用户名: " DOCKERHUB_USERNAME
        if [ -z "$DOCKERHUB_USERNAME" ]; then
          error "DockerHub用户名不能为空"
          PUSH_DOCKERHUB=false
        fi
      fi
      ;;
    *)
      error "无效的选项编号，使用默认选项：推送到个人服务器"
      PUSH_IMAGES=true
      PUSH_PERSONAL=true
      PUSH_GITHUB=false
      PUSH_DOCKERHUB=false
      select_personal_servers || PUSH_PERSONAL=false
      ;;
  esac

  return 0
}

# 检查是否有命令行参数
HAS_ARGS=false
if [ "$1" != "" ]; then
  HAS_ARGS=true
fi

# 如果没有命令行参数，则进行完整的交互式选择
if [ "$HAS_ARGS" = false ]; then
  select_build_type
  select_clean_option
  select_environment
else
  # 如果有命令行参数，则检查各个参数是否已经通过命令行指定
  # 构建类型参数检查
  BUILD_TYPE_SPECIFIED=false
  if [ "$BUILD_SERVER" = true ] || [ "$BUILD_WEB" = true ] || [ "$BUILD_ALL" = true ]; then
    BUILD_TYPE_SPECIFIED=true
  fi

  # 清理选项参数检查
  CLEAN_OPTION_SPECIFIED=false
  if [ "$SKIP_PRE_CLEAN" = true ]; then
    CLEAN_OPTION_SPECIFIED=true
  fi



  # 推送选项参数检查
  PUSH_OPTION_SPECIFIED=false
  if [ "$PUSH_IMAGES" = false ] || [ "$PUSH_GITHUB" = true ] || [ "$PUSH_DOCKERHUB" = true ]; then
    PUSH_OPTION_SPECIFIED=true
  fi

  # 确保推送选项的一致性
  if [ "$PUSH_IMAGES" = false ]; then
    # 这里不需要再次设置，因为已经在参数处理阶段设置过了
    info "已禁用所有推送选项，因为指定了 --no-push"
  fi

  # 根据是否指定了参数来决定是否进行交互式选择
  if [ "$BUILD_TYPE_SPECIFIED" = false ]; then
    select_build_type
  fi

  if [ "$CLEAN_OPTION_SPECIFIED" = false ]; then
    select_clean_option
  fi

  # 只有在需要推送且没有指定推送选项时才进行交互式选择
  if [ "$PUSH_IMAGES" = true ] && [ "$PUSH_OPTION_SPECIFIED" = false ]; then
    select_environment
  elif [ "$PUSH_IMAGES" = true ] && [ "$PUSH_PERSONAL" = true ] && [ ${#SELECTED_PERSONAL_SERVERS[@]} -eq 0 ]; then
    # 如果指定了推送到个人服务器但没有选择具体服务器，则进行交互式选择
    select_personal_servers || PUSH_PERSONAL=false
  fi
fi

# 显示配置信息
info "Docker镜像构建与推送配置:"
echo "-----------------------------------"
echo "镜像仓库地址: ${DOCKER_REGISTRY}"
echo "镜像名称前缀: ${IMAGE_PREFIX}"
echo "镜像标签: ${IMAGE_TAG}"

if [ "$BUILD_ALL" = true ]; then
  echo "构建类型: 所有镜像"
else
  echo "构建服务器镜像: ${BUILD_SERVER}"
  echo "构建Web界面镜像: ${BUILD_WEB}"
fi

echo "推送镜像: ${PUSH_IMAGES}"
echo "跳过构建前清理: ${SKIP_PRE_CLEAN}"

if [ "$PUSH_PERSONAL" = true ]; then
  echo "推送到个人服务器:"
  for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
    IFS=':' read -r server_name server_host server_cert <<< "${PERSONAL_SERVERS[$server_id]}"
    echo "  - $server_name ($server_host)"
  done
else
  echo "推送到个人服务器: 否"
fi

if [ "$PUSH_GITHUB" = true ]; then
  echo "推送到GitHub Container Registry: 是"
else
  echo "推送到GitHub Container Registry: 否"
fi

if [ "$PUSH_DOCKERHUB" = true ]; then
  echo "推送到DockerHub账户: ${DOCKERHUB_USERNAME}"
else
  echo "推送到DockerHub账户: 否"
fi
echo "-----------------------------------"

# 确认继续
if [ "$AUTO_CONFIRM" = true ]; then
  info "自动确认模式：跳过确认提示"
else
  read -p "是否继续? [Y/n] " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]] && [[ ! $REPLY = "" ]]; then
    info "操作已取消"
    exit 0
  fi
fi

# 创建Docker上下文函数
create_docker_context() {
  local context_name=$1
  local host=$2
  local cert_path=$3

  info "创建Docker上下文: ${context_name}"

  # 检查上下文是否已存在
  if docker context inspect $context_name &> /dev/null; then
    warn "上下文 ${context_name} 已存在，正在删除..."
    docker context rm $context_name
  fi

  # 验证证书文件是否存在
  if [ ! -f "${cert_path}/ca.pem" ]; then
    error "证书文件不存在: ${cert_path}/ca.pem"
    return 1
  fi

  if [ ! -f "${cert_path}/cert.pem" ]; then
    error "证书文件不存在: ${cert_path}/cert.pem"
    return 1
  fi

  if [ ! -f "${cert_path}/key.pem" ]; then
    error "证书文件不存在: ${cert_path}/key.pem"
    return 1
  fi

  info "证书文件验证成功，路径: ${cert_path}"

  # 创建新的上下文
  docker context create $context_name \
    --docker "host=$host,ca=$cert_path/ca.pem,cert=$cert_path/cert.pem,key=$cert_path/key.pem"

  if [ $? -ne 0 ]; then
    error "创建Docker上下文失败: ${context_name}"
    return 1
  else
    info "创建Docker上下文成功: ${context_name}"
    return 0
  fi
}

# 轻量级清理Docker缓存函数 - 构建前使用
light_clean_docker_cache() {
  if [ "$SKIP_PRE_CLEAN" = true ]; then
    info "跳过构建前的清理（按用户要求）..."
    return 0
  fi

  info "执行轻量级Docker缓存清理..."

  # 只删除与当前构建相关的悬空镜像，避免影响其他容器
  info "删除悬空镜像..."
  docker image prune -f --filter "dangling=true"

  # 轻度清理构建缓存 - 只清理超过30天未使用的缓存
  info "轻度清理构建缓存 - 清理长期未使用的缓存..."
  docker builder prune -f --filter "until=720h" --keep-storage=15GB > /dev/null 2>&1 || true

  info "保留最近使用的依赖层缓存以加速后续构建..."
  info "轻量级Docker缓存清理完成"
}

# 完全清理Docker缓存函数 - 成功构建和推送后使用
full_clean_docker_cache() {
  info "执行智能Docker缓存清理..."

  # 只删除与当前构建相关的镜像
  local image_pattern="${IMAGE_PREFIX}"
  info "删除与 ${image_pattern} 相关的未使用镜像..."
  docker images | grep "${image_pattern}" | awk '{print $3}' | xargs -r docker rmi 2>/dev/null || true

  # 删除悬空镜像（dangling images）
  info "删除悬空镜像..."
  docker image prune -f --filter "dangling=true"

  # 智能清理构建缓存 - 只保留最近7天使用的依赖层缓存，限制总大小为10GB
  info "智能清理构建缓存 - 只保留最近使用的依赖层..."

  # 清理超过7天未使用的构建缓存
  docker builder prune -f --filter "until=168h" --keep-storage=10GB

  info "保留最近使用的依赖层缓存以加速后续构建..."
  info "智能Docker缓存清理完成"
}



# 删除指定镜像函数
remove_local_image() {
  local image_name=$1

  if [ -n "$image_name" ]; then
    info "删除本地镜像: ${image_name}"
    docker rmi $image_name || true
  fi
}

# 推送到远程服务器函数
push_to_remote_server() {
  local context_name=$1
  local image_name=$2
  local remote_image_name=$3

  info "推送镜像到远程服务器: ${context_name}"

  # 保存镜像到文件
  local image_file="${image_name//[:\/]/_}.tar"
  info "保存镜像到文件: ${image_file}"

  docker save -o $image_file $image_name
  if [ $? -ne 0 ]; then
    error "保存镜像失败: ${image_name}"
    return 1
  fi

  # 切换到远程上下文
  info "切换到远程上下文: ${context_name}"
  docker context use $context_name

  # 加载镜像到远程服务器
  info "加载镜像到远程服务器: ${remote_image_name}"
  docker load -i $image_file

  if [ $? -ne 0 ]; then
    error "加载镜像到远程服务器失败"
    # 切回默认上下文
    docker context use default
    rm $image_file
    return 1
  fi

  # 给镜像打标签
  if [ -n "$remote_image_name" ] && [ "$remote_image_name" != "$image_name" ]; then
    info "为镜像打标签: ${remote_image_name}"
    docker tag $image_name $remote_image_name
  fi

  # 切回默认上下文
  docker context use default

  # 删除临时文件
  rm $image_file

  info "镜像已成功推送到远程服务器: ${context_name}"
  return 0
}



# 根据BUILD_ALL参数设置构建标志
if [ "$BUILD_ALL" = true ]; then
  BUILD_SERVER=true
  BUILD_WEB=true
fi

# 构建服务器镜像
if [ "$BUILD_SERVER" = true ]; then
  # 使用不带仓库前缀的本地镜像名
  SERVER_IMAGE_NAME="${IMAGE_PREFIX}:server_${IMAGE_TAG}"

  # 在构建前执行轻量级清理Docker缓存
  light_clean_docker_cache

  # 使用BuildKit构建本地镜像，然后推送
  info "开始构建服务器镜像: ${SERVER_IMAGE_NAME}"

  # 设置构建时的镜像源参数
  # 从.env文件中读取镜像源配置
  PIP_MIRROR="${PIP_MIRROR:-https://pypi.tuna.tsinghua.edu.cn/simple}"
  NPM_MIRROR="${NPM_MIRROR:-https://registry.npmmirror.com}"

  # 设置构建参数
  BUILD_ARGS="--build-arg PIP_INDEX_URL=${PIP_MIRROR} --build-arg NPM_REGISTRY=${NPM_MIRROR}"



  info "使用镜像源进行构建"
  build_success=false

  # 构建服务器镜像
  info "构建服务器镜像..."

  # 设置构建参数
  BUILD_ARGS="${BUILD_ARGS}"

  if docker buildx build ${BUILD_ARGS} \
     --platform linux/amd64 \
     --pull \
     --load \
     --build-arg BUILDKIT_INLINE_CACHE=1 \
     -t "${SERVER_IMAGE_NAME}" -f Dockerfile-server .; then
    build_success=true
  fi

  if [ "$build_success" = "true" ]; then
    info "服务器镜像构建成功: ${SERVER_IMAGE_NAME}"

    # 推送标志，用于跟踪是否至少有一次成功推送
    server_push_success=false

    # 推送到个人服务器
    if [ "$PUSH_PERSONAL" = true ]; then
      for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
        # 根据server_id获取对应的服务器信息
        if [ "$server_id" = "prod" ]; then
          server_info="${PERSONAL_SERVER_PROD}"
        elif [ "$server_id" = "test" ]; then
          server_info="${PERSONAL_SERVER_TEST}"
        else
          error "未知的服务器ID: ${server_id}"
          continue
        fi

        IFS=':' read -r server_name server_host server_cert <<< "${server_info}"

        CONTEXT_NAME="personal-${server_id}"
        info "推送服务器镜像到 ${server_name}..."

        if create_docker_context "$CONTEXT_NAME" "https://${server_host}:2376" "$server_cert"; then
          if push_to_remote_server "$CONTEXT_NAME" "${SERVER_IMAGE_NAME}" "${SERVER_IMAGE_NAME}"; then
            server_push_success=true
          fi
        fi
      done
    fi

    # 推送到GitHub Container Registry
    if [ "$PUSH_GITHUB" = true ]; then
      GITHUB_SERVER_IMAGE="${GITHUB_REGISTRY}/${IMAGE_PREFIX}:server_${IMAGE_TAG}"
      info "推送服务器镜像到GitHub Container Registry..."
      info "为镜像打标签: ${GITHUB_SERVER_IMAGE}"

      if docker tag "${SERVER_IMAGE_NAME}" "${GITHUB_SERVER_IMAGE}"; then
        if ! docker push "${GITHUB_SERVER_IMAGE}"; then
          warn "服务器镜像推送到GitHub Container Registry失败"
        else
          info "服务器镜像推送到GitHub Container Registry成功"
          server_push_success=true
        fi
      else
        warn "为服务器镜像打GitHub标签失败"
      fi
    fi

    # 推送到DockerHub
    if [ "$PUSH_DOCKERHUB" = true ]; then
      DOCKERHUB_SERVER_IMAGE="${DOCKERHUB_REGISTRY}/${DOCKERHUB_USERNAME}/${IMAGE_PREFIX}:server_${IMAGE_TAG}"
      info "推送服务器镜像到DockerHub..."
      info "为镜像打标签: ${DOCKERHUB_SERVER_IMAGE}"

      if docker tag "${SERVER_IMAGE_NAME}" "${DOCKERHUB_SERVER_IMAGE}"; then
        if ! docker push "${DOCKERHUB_SERVER_IMAGE}"; then
          warn "服务器镜像推送到DockerHub失败"
        else
          info "服务器镜像推送到DockerHub成功"
          server_push_success=true
        fi
      else
        warn "为服务器镜像打DockerHub标签失败"
      fi
    fi

    # 如果至少有一次成功推送，则删除本地镜像并执行完全清理
    if [ "$server_push_success" = true ] || [ "$PUSH_IMAGES" = false ]; then
      # 删除本地镜像
      info "清理本地服务器镜像..."

      # 删除GitHub标签的镜像
      if [ "$PUSH_GITHUB" = true ]; then
        remove_local_image "${GITHUB_SERVER_IMAGE}"
      fi

      # 删除DockerHub标签的镜像
      if [ "$PUSH_DOCKERHUB" = true ]; then
        remove_local_image "${DOCKERHUB_SERVER_IMAGE}"
      fi

      # 删除原始镜像
      remove_local_image "${SERVER_IMAGE_NAME}"

      # 执行完全清理
      if [ "$PUSH_IMAGES" = true ]; then
        info "服务器镜像推送成功，执行完全清理..."
        full_clean_docker_cache
      fi
    else
      warn "由于没有成功推送，保留本地服务器镜像以便调试"
    fi
  else
    error "服务器镜像构建失败"
    exit 1
  fi
fi

# 构建Web界面镜像
if [ "$BUILD_WEB" = true ]; then
  # 使用不带仓库前缀的本地镜像名
  WEB_IMAGE_NAME="${IMAGE_PREFIX}:web_${IMAGE_TAG}"

  # 在构建前执行轻量级清理Docker缓存
  light_clean_docker_cache

  # 使用BuildKit构建本地镜像，然后推送
  info "开始构建Web界面镜像: ${WEB_IMAGE_NAME}"

  # 设置构建时的镜像源参数
  # 从.env文件中读取镜像源配置
  PIP_MIRROR="${PIP_MIRROR:-https://pypi.tuna.tsinghua.edu.cn/simple}"
  NPM_MIRROR="${NPM_MIRROR:-https://registry.npmmirror.com}"

  # 设置构建参数
  BUILD_ARGS="--build-arg PIP_INDEX_URL=${PIP_MIRROR} --build-arg NPM_REGISTRY=${NPM_MIRROR}"



  info "使用镜像源进行构建"
  build_success=false

  # 构建Web界面镜像
  info "构建Web界面镜像..."

  # 设置构建参数
  BUILD_ARGS="${BUILD_ARGS}"

  if docker buildx build ${BUILD_ARGS} \
     --platform linux/amd64 \
     --pull \
     --load \
     --build-arg BUILDKIT_INLINE_CACHE=1 \
     -t "${WEB_IMAGE_NAME}" -f Dockerfile-web .; then
    build_success=true
  fi

  if [ "$build_success" = "true" ]; then
    info "Web界面镜像构建成功: ${WEB_IMAGE_NAME}"

    # 推送标志，用于跟踪是否至少有一次成功推送
    web_push_success=false

    # 推送到个人服务器
    if [ "$PUSH_PERSONAL" = true ]; then
      for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
        # 根据server_id获取对应的服务器信息
        if [ "$server_id" = "prod" ]; then
          server_info="${PERSONAL_SERVER_PROD}"
        elif [ "$server_id" = "test" ]; then
          server_info="${PERSONAL_SERVER_TEST}"
        else
          error "未知的服务器ID: ${server_id}"
          continue
        fi

        IFS=':' read -r server_name server_host server_cert <<< "${server_info}"

        CONTEXT_NAME="personal-${server_id}"
        info "推送Web界面镜像到 ${server_name}..."

        # 检查上下文是否已存在（可能已被服务器镜像推送创建）
        if ! docker context inspect "$CONTEXT_NAME" &> /dev/null; then
          if ! create_docker_context "$CONTEXT_NAME" "https://${server_host}:2376" "$server_cert"; then
            continue
          fi
        fi

        if push_to_remote_server "$CONTEXT_NAME" "${WEB_IMAGE_NAME}" "${WEB_IMAGE_NAME}"; then
          web_push_success=true
        fi
      done
    fi

    # 推送到GitHub Container Registry
    if [ "$PUSH_GITHUB" = true ]; then
      GITHUB_WEB_IMAGE="${GITHUB_REGISTRY}/${IMAGE_PREFIX}:web_${IMAGE_TAG}"
      info "推送Web界面镜像到GitHub Container Registry..."
      info "为镜像打标签: ${GITHUB_WEB_IMAGE}"

      if docker tag "${WEB_IMAGE_NAME}" "${GITHUB_WEB_IMAGE}"; then
        if ! docker push "${GITHUB_WEB_IMAGE}"; then
          warn "Web界面镜像推送到GitHub Container Registry失败"
        else
          info "Web界面镜像推送到GitHub Container Registry成功"
          web_push_success=true
        fi
      else
        warn "为Web界面镜像打GitHub标签失败"
      fi
    fi

    # 推送到DockerHub
    if [ "$PUSH_DOCKERHUB" = true ]; then
      DOCKERHUB_WEB_IMAGE="${DOCKERHUB_REGISTRY}/${DOCKERHUB_USERNAME}/${IMAGE_PREFIX}:web_${IMAGE_TAG}"
      info "推送Web界面镜像到DockerHub..."
      info "为镜像打标签: ${DOCKERHUB_WEB_IMAGE}"

      if docker tag "${WEB_IMAGE_NAME}" "${DOCKERHUB_WEB_IMAGE}"; then
        if ! docker push "${DOCKERHUB_WEB_IMAGE}"; then
          warn "Web界面镜像推送到DockerHub失败"
        else
          info "Web界面镜像推送到DockerHub成功"
          web_push_success=true
        fi
      else
        warn "为Web界面镜像打DockerHub标签失败"
      fi
    fi

    # 如果至少有一次成功推送，则删除本地镜像并执行完全清理
    if [ "$web_push_success" = true ] || [ "$PUSH_IMAGES" = false ]; then
      # 删除本地镜像
      info "清理本地Web界面镜像..."

      # 删除GitHub标签的镜像
      if [ "$PUSH_GITHUB" = true ]; then
        remove_local_image "${GITHUB_WEB_IMAGE}"
      fi

      # 删除DockerHub标签的镜像
      if [ "$PUSH_DOCKERHUB" = true ]; then
        remove_local_image "${DOCKERHUB_WEB_IMAGE}"
      fi

      # 删除原始镜像
      remove_local_image "${WEB_IMAGE_NAME}"

      # 执行完全清理
      if [ "$PUSH_IMAGES" = true ]; then
        info "Web界面镜像推送成功，执行完全清理..."
        full_clean_docker_cache
      fi
    else
      warn "由于没有成功推送，保留本地Web界面镜像以便调试"
    fi
  else
    error "Web界面镜像构建失败"
    exit 1
  fi
fi

# 清理上下文
if [ "$PUSH_PERSONAL" = true ]; then
  for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
    CONTEXT_NAME="personal-${server_id}"
    if docker context inspect "$CONTEXT_NAME" &> /dev/null; then
      info "清理Docker上下文: ${CONTEXT_NAME}"
      docker context use default
      docker context rm "$CONTEXT_NAME"
    fi
  done
fi

# 总结
info "操作完成!"
echo "-----------------------------------"
if [ "$BUILD_SERVER" = true ]; then
  echo "服务器镜像: ${SERVER_IMAGE_NAME}"

  if [ "$PUSH_PERSONAL" = true ]; then
    echo "  - 已推送到个人服务器:"
    for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
      # 根据server_id获取对应的服务器信息
      if [ "$server_id" = "prod" ]; then
        server_info="${PERSONAL_SERVER_PROD}"
      elif [ "$server_id" = "test" ]; then
        server_info="${PERSONAL_SERVER_TEST}"
      else
        error "未知的服务器ID: ${server_id}"
        continue
      fi

      IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
      echo "    * ${server_name} (${server_host}): ${SERVER_IMAGE_NAME}"
    done
  fi

  if [ "$PUSH_GITHUB" = true ]; then
    echo "  - 已推送到GitHub Container Registry: ${GITHUB_SERVER_IMAGE}"
  fi

  if [ "$PUSH_DOCKERHUB" = true ]; then
    echo "  - 已推送到DockerHub: ${DOCKERHUB_SERVER_IMAGE}"
  fi
fi

if [ "$BUILD_WEB" = true ]; then
  echo "Web界面镜像: ${WEB_IMAGE_NAME}"

  if [ "$PUSH_PERSONAL" = true ]; then
    echo "  - 已推送到个人服务器:"
    for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
      # 根据server_id获取对应的服务器信息
      if [ "$server_id" = "prod" ]; then
        server_info="${PERSONAL_SERVER_PROD}"
      elif [ "$server_id" = "test" ]; then
        server_info="${PERSONAL_SERVER_TEST}"
      else
        error "未知的服务器ID: ${server_id}"
        continue
      fi

      IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
      echo "    * ${server_name} (${server_host}): ${WEB_IMAGE_NAME}"
    done
  fi

  if [ "$PUSH_GITHUB" = true ]; then
    echo "  - 已推送到GitHub Container Registry: ${GITHUB_WEB_IMAGE}"
  fi

  if [ "$PUSH_DOCKERHUB" = true ]; then
    echo "  - 已推送到DockerHub: ${DOCKERHUB_WEB_IMAGE}"
  fi
fi
echo "-----------------------------------"

info "您可以使用以下命令拉取镜像:"
if [ "$BUILD_SERVER" = true ]; then
  if [ "$PUSH_PERSONAL" = true ]; then
    for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
      # 根据server_id获取对应的服务器信息
      if [ "$server_id" = "prod" ]; then
        server_info="${PERSONAL_SERVER_PROD}"
      elif [ "$server_id" = "test" ]; then
        server_info="${PERSONAL_SERVER_TEST}"
      else
        error "未知的服务器ID: ${server_id}"
        continue
      fi

      IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
      echo "  # 从${server_name}拉取服务器镜像:"
      # 在个人服务器上，镜像名称保持不变，但需要连接到对应的Docker上下文
      echo "  # 首先创建Docker上下文:"
      echo "  docker context create ${server_id} --docker \"host=https://${server_host}:2376,ca=${server_cert}/ca.pem,cert=${server_cert}/cert.pem,key=${server_cert}/key.pem\""
      echo "  # 然后使用该上下文拉取镜像:"
      echo "  docker --context ${server_id} pull ${SERVER_IMAGE_NAME}"
    done
  fi
  [ "$PUSH_GITHUB" = true ] && echo "  # 从GitHub拉取服务器镜像:"
  [ "$PUSH_GITHUB" = true ] && echo "  docker pull ${GITHUB_SERVER_IMAGE}"
  [ "$PUSH_DOCKERHUB" = true ] && echo "  # 从DockerHub拉取服务器镜像:"
  [ "$PUSH_DOCKERHUB" = true ] && echo "  docker pull ${DOCKERHUB_SERVER_IMAGE}"
fi

if [ "$BUILD_WEB" = true ]; then
  if [ "$PUSH_PERSONAL" = true ]; then
    for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
      # 根据server_id获取对应的服务器信息
      if [ "$server_id" = "prod" ]; then
        server_info="${PERSONAL_SERVER_PROD}"
      elif [ "$server_id" = "test" ]; then
        server_info="${PERSONAL_SERVER_TEST}"
      else
        error "未知的服务器ID: ${server_id}"
        continue
      fi

      IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
      echo "  # 从${server_name}拉取Web界面镜像:"
      # 在个人服务器上，镜像名称保持不变，但需要连接到对应的Docker上下文
      echo "  # 使用已创建的Docker上下文拉取镜像:"
      echo "  docker --context ${server_id} pull ${WEB_IMAGE_NAME}"
    done
  fi
  [ "$PUSH_GITHUB" = true ] && echo "  # 从GitHub拉取Web界面镜像:"
  [ "$PUSH_GITHUB" = true ] && echo "  docker pull ${GITHUB_WEB_IMAGE}"
  [ "$PUSH_DOCKERHUB" = true ] && echo "  # 从DockerHub拉取Web界面镜像:"
  [ "$PUSH_DOCKERHUB" = true ] && echo "  docker pull ${DOCKERHUB_WEB_IMAGE}"
fi

echo "-----------------------------------"

# 最终清理
if [ "$PUSH_IMAGES" = true ] && { [ "$BUILD_SERVER" = false ] || [ "$BUILD_WEB" = false ]; }; then
  # 如果只构建了一种镜像并且成功推送，执行轻量级清理
  info "执行最终轻量级清理..."
  docker container prune -f
  docker image prune -f
else
  # 如果没有推送镜像或者构建了所有类型的镜像，不需要额外清理
  # 因为每个成功的构建和推送后已经执行了完全清理
  info "跳过最终清理，因为已经在各个镜像推送成功后执行了清理"
fi

info "脚本执行完成！"
info "如果需要重新执行失败的构建，请使用 --skip-pre-clean 参数跳过构建前的清理"
echo "-----------------------------------"
info "缓存管理提示:"
info "1. 当前脚本会自动清理过期缓存，保留最近使用的依赖层"
info "2. 如果磁盘空间紧张，可以手动执行以下命令清理更多缓存:"
echo "   docker builder prune -f --keep-storage=5GB  # 保留5GB的构建缓存"
info "3. 如果需要完全清理所有缓存（会导致下次构建变慢）:"
echo "   docker builder prune -a -f  # 清理所有构建缓存"
echo "-----------------------------------"
exit 0