package xiaozhi.modules.quota.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.quota.entity.MacBlacklistEntity;

import java.util.List;

/**
 * MAC地址黑名单DAO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Mapper
public interface MacBlacklistDao extends BaseMapper<MacBlacklistEntity> {

    /**
     * 获取所有MAC地址
     *
     * @return MAC地址列表
     */
    @Select("SELECT mac_address FROM ai_mac_blacklist WHERE expire_date IS NULL OR expire_date > NOW()")
    List<String> getAllMacAddresses();

    /**
     * 分页获取黑名单MAC地址列表
     *
     * @param offset 偏移量
     * @param limit 每页数量
     * @return MAC地址列表
     */
    @Select("SELECT mac_address FROM ai_mac_blacklist WHERE expire_date IS NULL OR expire_date > NOW() LIMIT #{offset}, #{limit}")
    List<String> getMacAddressesByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取黑名单MAC地址总数
     *
     * @return MAC地址总数
     */
    @Select("SELECT COUNT(*) FROM ai_mac_blacklist WHERE expire_date IS NULL OR expire_date > NOW()")
    int getMacAddressCount();

    /**
     * 检查MAC地址是否在黑名单中
     *
     * @param macAddress MAC地址
     * @return 是否在黑名单中
     */
    @Select("SELECT COUNT(*) FROM ai_mac_blacklist WHERE mac_address = #{macAddress} AND (expire_date IS NULL OR expire_date > NOW())")
    int checkMacAddressInBlacklist(String macAddress);

    /**
     * 批量检查MAC地址是否在黑名单中
     *
     * @param macAddresses MAC地址列表
     * @return 黑名单中的MAC地址列表
     */
    @Select("<script>SELECT mac_address FROM ai_mac_blacklist WHERE (expire_date IS NULL OR expire_date > NOW()) AND mac_address IN " +
            "<foreach collection='macAddresses' item='mac' open='(' separator=',' close=')'>" +
            "#{mac}" +
            "</foreach></script>")
    List<String> batchCheckMacAddressesInBlacklist(@Param("macAddresses") List<String> macAddresses);
}
