package xiaozhi.modules.quota.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 配额设置实体
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_quota_settings")
public class QuotaSettingsEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 配额类型(device/account)
     */
    private String quotaType;

    /**
     * 配额值(0表示不限制)
     */
    private Integer quotaValue;

    /**
     * 重置类型(daily/monthly/never)
     */
    private String resetType;

    /**
     * 配额描述
     */
    private String description;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
