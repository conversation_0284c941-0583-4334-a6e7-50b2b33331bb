package xiaozhi.modules.quota.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import xiaozhi.modules.quota.dto.QuotaUsageDTO;
import xiaozhi.modules.quota.entity.QuotaUsageEntity;

import java.util.Date;
import java.util.List;

/**
 * 配额使用记录DAO
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Mapper
public interface QuotaUsageDao extends BaseMapper<QuotaUsageEntity> {
    
    /**
     * 分页查询配额使用记录
     * 
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<QuotaUsageDTO> page(Page<QuotaUsageDTO> page, @Param("params") Object params);
    
    /**
     * 获取用户当日配额使用情况
     * 
     * @param userId 用户ID
     * @param usageDate 使用日期
     * @return 配额使用情况
     */
    @Select("SELECT SUM(usage_value) FROM ai_quota_usage WHERE user_id = #{userId} AND usage_date = #{usageDate}")
    Integer getUserDailyUsage(@Param("userId") Long userId, @Param("usageDate") Date usageDate);
    
    /**
     * 获取设备当日配额使用情况
     * 
     * @param deviceMac 设备MAC地址
     * @param usageDate 使用日期
     * @return 配额使用情况
     */
    @Select("SELECT SUM(usage_value) FROM ai_quota_usage WHERE device_mac = #{deviceMac} AND usage_date = #{usageDate}")
    Integer getDeviceDailyUsage(@Param("deviceMac") String deviceMac, @Param("usageDate") Date usageDate);
}
