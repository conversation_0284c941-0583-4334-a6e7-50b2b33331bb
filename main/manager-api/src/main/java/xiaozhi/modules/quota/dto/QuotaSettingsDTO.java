package xiaozhi.modules.quota.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 配额设置DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@Schema(description = "配额设置")
public class QuotaSettingsDTO {
    
    @Schema(description = "设备默认配额(0表示不限制)")
    @NotNull(message = "设备默认配额不能为空")
    @Min(value = 0, message = "设备默认配额不能小于0")
    private Integer deviceDefaultQuota;
    
    @Schema(description = "账号默认配额(0表示不限制)")
    @NotNull(message = "账号默认配额不能为空")
    @Min(value = 0, message = "账号默认配额不能小于0")
    private Integer accountDefaultQuota;
    
    @Schema(description = "设备配额重置类型(daily/monthly/never)")
    @NotBlank(message = "设备配额重置类型不能为空")
    private String deviceResetType;
    
    @Schema(description = "账号配额重置类型(daily/monthly/never)")
    @NotBlank(message = "账号配额重置类型不能为空")
    private String accountResetType;
    
    @Schema(description = "是否启用配额限制")
    @NotNull(message = "是否启用配额限制不能为空")
    private Boolean enableQuotaLimit;
}
