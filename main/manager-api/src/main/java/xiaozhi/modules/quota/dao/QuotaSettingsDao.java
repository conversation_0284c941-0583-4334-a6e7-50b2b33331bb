package xiaozhi.modules.quota.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.quota.entity.QuotaSettingsEntity;

/**
 * 配额设置DAO
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Mapper
public interface QuotaSettingsDao extends BaseMapper<QuotaSettingsEntity> {
    
    /**
     * 根据配额类型获取配额设置
     * 
     * @param quotaType 配额类型
     * @return 配额设置
     */
    @Select("SELECT * FROM ai_quota_settings WHERE quota_type = #{quotaType}")
    QuotaSettingsEntity getByQuotaType(String quotaType);
}
