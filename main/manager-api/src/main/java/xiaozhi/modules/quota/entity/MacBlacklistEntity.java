package xiaozhi.modules.quota.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * MAC地址黑名单实体
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_mac_blacklist")
public class MacBlacklistEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * MAC地址
     */
    private String macAddress;

    /**
     * 禁用原因
     */
    private String reason;

    /**
     * 过期时间(NULL表示永久)
     */
    private Date expireDate;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
