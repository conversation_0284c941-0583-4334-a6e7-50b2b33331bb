package xiaozhi.modules.quota.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 配额限制DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@Schema(description = "配额限制")
public class QuotaLimitDTO {
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "智能体ID")
    private String agentId;
    
    @Schema(description = "MAC地址")
    private String macAddress;
    
    @Schema(description = "是否为官方默认智能体")
    private Boolean isDefault;
    
    @Schema(description = "账号每日总额度")
    private Integer accountDailyLimit;
    
    @Schema(description = "设备每日限额（仅官方默认智能体）")
    private Integer deviceDailyLimit;
    
    @Schema(description = "当前已使用额度")
    private Integer usedQuota;
    
    @Schema(description = "剩余可用额度")
    private Integer remainingQuota;
    
    @Schema(description = "限制类型: account-账号级别, device-设备级别")
    private String limitType;
} 