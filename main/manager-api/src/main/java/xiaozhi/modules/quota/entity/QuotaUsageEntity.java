package xiaozhi.modules.quota.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 配额使用记录实体
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_quota_usage")
@Schema(description = "配额使用记录")
public class QuotaUsageEntity {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 用户ID - 逻辑外键关联sys_user表的id字段
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 设备MAC地址 - 逻辑外键关联ai_device表的mac_address字段
     */
    @Schema(description = "设备MAC地址")
    private String deviceMac;

    /**
     * 智能体ID - 逻辑外键关联ai_agent表的id字段
     */
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 使用类型(input/output)
     */
    @Schema(description = "使用类型")
    private String usageType;

    /**
     * 输入Token数量
     */
    @Schema(description = "输入Token数量")
    private Integer inputTokens;

    /**
     * 输出Token数量
     */
    @Schema(description = "输出Token数量")
    private Integer outputTokens;

    /**
     * Token总量 (inputTokens + outputTokens)
     * 可作为计算字段或冗余存储
     */
    @Schema(description = "Token总量")
    private Integer totalTokens;

    /**
     * 模型名称 - 用于不同模型Token计费
     */
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * @deprecated 使用totalTokens替代
     */
    @Deprecated
    @Schema(description = "使用量(已废弃，使用totalTokens替代)")
    private Integer usageValue;

    /**
     * 使用日期
     */
    @Schema(description = "使用日期")
    private Date usageDate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createDate;
}
