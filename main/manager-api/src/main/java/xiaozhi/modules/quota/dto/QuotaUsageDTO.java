package xiaozhi.modules.quota.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 配额使用记录DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@Schema(description = "配额使用记录")
public class QuotaUsageDTO implements Serializable {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "设备MAC地址")
    private String deviceMac;
    
    @Schema(description = "设备别名")
    private String deviceAlias;
    
    @Schema(description = "智能体ID")
    private String agentId;
    
    @Schema(description = "智能体名称")
    private String agentName;
    
    @Schema(description = "使用类型(input/output)")
    private String usageType;
    
    @Schema(description = "使用量")
    private Integer usageValue;
    
    @Schema(description = "使用日期")
    private Date usageDate;
    
    @Schema(description = "创建时间")
    private Date createDate;
}
