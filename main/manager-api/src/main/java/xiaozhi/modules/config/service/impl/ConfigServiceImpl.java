package xiaozhi.modules.config.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lombok.AllArgsConstructor;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.exception.ErrorCode;
import xiaozhi.common.exception.RenException;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.JsonUtils;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentService;

import xiaozhi.modules.agent.service.AgentTemplateService;
import xiaozhi.modules.config.service.ConfigService;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.quota.dao.MacBlacklistDao;
import xiaozhi.modules.model.entity.ModelConfigEntity;
import xiaozhi.modules.model.service.ModelConfigService;
import xiaozhi.modules.sys.dto.SysParamsDTO;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.timbre.service.TimbreService;
import xiaozhi.modules.timbre.vo.TimbreDetailsVO;

@Service
@AllArgsConstructor
public class ConfigServiceImpl implements ConfigService {
    private static final Logger log = LoggerFactory.getLogger(ConfigServiceImpl.class);

    private final SysParamsService sysParamsService;
    private final DeviceService deviceService;
    private final ModelConfigService modelConfigService;
    private final AgentService agentService;
    private final AgentTemplateService agentTemplateService;
    private final RedisUtils redisUtils;
    private final TimbreService timbreService;
    private final MacBlacklistDao macBlacklistDao;

    @Override
    public Object getConfig(Boolean isCache) {
        if (isCache) {
            // 先从Redis获取配置
            Object cachedConfig = redisUtils.get(RedisKeys.getServerConfigKey());
            if (cachedConfig != null) {
                return cachedConfig;
            }
        }

        // 构建配置信息
        Map<String, Object> result = new HashMap<>();
        buildConfig(result);

        // 查询默认智能体
        AgentTemplateEntity agent = agentTemplateService.getDefaultTemplate();
        if (agent == null) {
            throw new RenException("默认智能体未找到");
        }

        // 构建模块配置
        buildModuleConfig(
                null,
                null,
                null,
                null,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                null,
                null,
                null,
                null,
                null,
                result,
                isCache);

        // 将配置存入Redis
        redisUtils.set(RedisKeys.getServerConfigKey(), result);

        return result;
    }

    @Override
    public Map<String, Object> getAgentModels(String macAddress, Map<String, String> selectedModule) {
        log.info("开始获取设备配置，MAC地址: {}", macAddress);

        // 根据MAC地址查找设备
        DeviceEntity device = deviceService.getDeviceByMacAddress(macAddress);
        if (device == null) {
            log.info("设备表中未找到MAC地址: {}，检查认证模式", macAddress);

            // 获取认证配置
            String macAuthEnabled = sysParamsService.getValue("mac_auth.enabled", false);
            boolean isMacAuthEnabled = "true".equalsIgnoreCase(macAuthEnabled);

            if (isMacAuthEnabled) {
                // MAC认证模式：这是设备认证阶段，需要检查黑名单和自动注册
                log.info("MAC认证模式 - 设备 {} 不在设备表中，开始认证流程", macAddress);

                // 1. 检查是否在黑名单中（认证的必要步骤）
                if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
                    log.info("MAC认证失败，设备 {} 在黑名单中", macAddress);
                    throw new RenException(ErrorCode.MAC_AUTH_FAILED, "Device not activated. Please visit our website to activate your device.");
                }

                // 2. 检查是否启用自动注册（统一使用mac_auth.auto_register）
                String autoRegisterEnabled = sysParamsService.getValue("mac_auth.auto_register", false);
                boolean isAutoRegisterEnabled = "true".equalsIgnoreCase(autoRegisterEnabled);

                if (isAutoRegisterEnabled) {
                    // 自动注册设备到设备表
                    log.info("MAC认证模式 - 启用自动注册，为设备 {} 创建设备记录", macAddress);
                    boolean registered = deviceService.bindDeviceToDefaultAgent(macAddress);
                    if (registered) {
                        log.info("MAC认证成功，设备 {} 自动注册完成，重新查询设备信息", macAddress);
                        // 自动注册成功后，重新查询设备信息，然后继续正常的配置获取流程
                        device = deviceService.getDeviceByMacAddress(macAddress);
                        if (device == null) {
                            log.error("自动注册成功但重新查询设备失败: {}", macAddress);
                            throw new RenException(ErrorCode.MAC_AUTH_FAILED, "Device registration error. Please try again.");
                        }
                        log.info("设备 {} 自动注册成功，继续正常配置获取流程", macAddress);
                        // 继续执行下面的正常流程，不要直接返回
                    } else {
                        log.warn("MAC认证失败，设备 {} 自动注册失败", macAddress);
                        throw new RenException(ErrorCode.MAC_AUTH_FAILED, "Device not activated. Please visit our website to activate your device.");
                    }
                } else {
                    // 未启用自动注册，返回错误
                    log.info("MAC认证失败，设备 {} 未在设备表中且未启用自动注册", macAddress);
                    throw new RenException(ErrorCode.MAC_AUTH_FAILED, "Device not activated. Please visit our website to activate your device.");
                }
            } else {
                // Token认证模式：为未注册设备生成绑定码
				// 如果设备，去redis里看看有没有需要连接的设备
                log.info("Token认证模式 - 设备 {} 未绑定，生成绑定码", macAddress);
                String cachedCode = deviceService.geCodeByDeviceId(macAddress);
                if (StringUtils.isNotBlank(cachedCode)) {
                    log.info("Token认证失败，设备 {} 需要绑定，绑定码: {}", macAddress, cachedCode);
                    throw new RenException(ErrorCode.OTA_DEVICE_NEED_BIND, cachedCode);
                }
                throw new RenException(ErrorCode.OTA_DEVICE_NOT_FOUND, "not found device");
            }
        }

        // 获取智能体信息
        AgentEntity agent = agentService.getAgentById(device.getAgentId());
        if (agent == null) {
            throw new RenException("智能体未找到");
        }
        // 获取音色信息
        String voice = null;
        TimbreDetailsVO timbre = timbreService.get(agent.getTtsVoiceId());
        if (timbre != null) {
            voice = timbre.getTtsVoice();
        }
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        // 获取单台设备每天最多输出字数
        String deviceMaxOutputSize = sysParamsService.getValue("device_max_output_size", true);
        if (deviceMaxOutputSize == null) {
            deviceMaxOutputSize = "10000"; // 默认值
        }
        result.put("device_max_output_size", deviceMaxOutputSize);

        // 添加Token配额相关配置
        addTokenQuotaConfig(result);

        // 添加MAC认证相关配置
        addMacAuthConfig(result);

        // 确保认证配置正确（二选一原则）
        ensureAuthConfig(result);

        // 获取聊天记录配置
        Integer chatHistoryConf = agent.getChatHistoryConf();
        if (agent.getMemModelId() != null && agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.IGNORE.getCode();
        } else if (agent.getMemModelId() != null
                && !agent.getMemModelId().equals(Constant.MEMORY_NO_MEM)
                && agent.getChatHistoryConf() == null) {
            chatHistoryConf = Constant.ChatHistoryConfEnum.RECORD_TEXT_AUDIO.getCode();
        }
        result.put("chat_history_conf", chatHistoryConf);

        // 添加智能体信息（用于Python端判断配额类型）
        Map<String, Object> agentInfo = new HashMap<>();
        agentInfo.put("agent_id", agent.getId());
        agentInfo.put("agent_name", agent.getAgentName());

        // 根据智能体实际类型动态设置agentInfo
        boolean isDefaultAgent = agent.isDefaultAgent();

        agentInfo.put("is_default_agent", isDefaultAgent);
        agentInfo.put("agent_type", agent.getAgentType());
        agentInfo.put("user_id", agent.getUserId()); // 直接使用数据库值，默认智能体本身就是null

        // 记录配额类型日志
        if (isDefaultAgent) {
            log.debug("设备 {} 使用默认智能体，配额类型：设备级", macAddress);
        } else {
            log.debug("设备 {} 使用用户智能体，配额类型：账号级", macAddress);
        }

        result.put("agent_info", agentInfo);
        // 如果客户端已实例化模型，则不返回
        String alreadySelectedVadModelId = (String) selectedModule.get("VAD");
        if (alreadySelectedVadModelId != null && alreadySelectedVadModelId.equals(agent.getVadModelId())) {
            agent.setVadModelId(null);
        }
        String alreadySelectedAsrModelId = (String) selectedModule.get("ASR");
        if (alreadySelectedAsrModelId != null && alreadySelectedAsrModelId.equals(agent.getAsrModelId())) {
            agent.setAsrModelId(null);
        }

        // 构建模块配置
        buildModuleConfig(
                agent.getAgentName(),
                agent.getSystemPrompt(),
                agent.getSummaryMemory(),
                voice,
                agent.getVadModelId(),
                agent.getAsrModelId(),
                agent.getLlmModelId(),
                agent.getVllmModelId(),
                agent.getTtsModelId(),
                agent.getMemModelId(),
                agent.getIntentModelId(),
                result,
                true);

        return result;
    }

    /**
     * 构建配置信息
     * 
     * @param paramsList 系统参数列表
     * @return 配置信息
     */
    private Object buildConfig(Map<String, Object> config) {

        // 查询所有系统参数
        List<SysParamsDTO> paramsList = sysParamsService.list(new HashMap<>());

        for (SysParamsDTO param : paramsList) {
            String[] keys = param.getParamCode().split("\\.");
            Map<String, Object> current = config;

            // 遍历除最后一个key之外的所有key
            for (int i = 0; i < keys.length - 1; i++) {
                String key = keys[i];
                if (!current.containsKey(key)) {
                    current.put(key, new HashMap<String, Object>());
                }
                current = (Map<String, Object>) current.get(key);
            }

            // 处理最后一个key
            String lastKey = keys[keys.length - 1];
            String value = param.getParamValue();

            // 根据valueType转换值
            switch (param.getValueType().toLowerCase()) {
                case "number":
                    try {
                        double doubleValue = Double.parseDouble(value);
                        // 如果数值是整数形式，则转换为Integer
                        if (doubleValue == (int) doubleValue) {
                            current.put(lastKey, (int) doubleValue);
                        } else {
                            current.put(lastKey, doubleValue);
                        }
                    } catch (NumberFormatException e) {
                        current.put(lastKey, value);
                    }
                    break;
                case "boolean":
                    current.put(lastKey, Boolean.parseBoolean(value));
                    break;
                case "array":
                    // 将分号分隔的字符串转换为数字数组
                    List<String> list = new ArrayList<>();
                    for (String num : value.split(";")) {
                        if (StringUtils.isNotBlank(num)) {
                            list.add(num.trim());
                        }
                    }
                    current.put(lastKey, list);
                    break;
                case "json":
                    try {
                        current.put(lastKey, JsonUtils.parseObject(value, Object.class));
                    } catch (Exception e) {
                        current.put(lastKey, value);
                    }
                    break;
                default:
                    current.put(lastKey, value);
            }
        }

        return config;
    }

    /**
     * 构建模块配置
     * 
     * @param prompt        提示词
     * @param voice         音色
     * @param vadModelId    VAD模型ID
     * @param asrModelId    ASR模型ID
     * @param llmModelId    LLM模型ID
     * @param ttsModelId    TTS模型ID
     * @param memModelId    记忆模型ID
     * @param intentModelId 意图模型ID
     * @param result        结果Map
     */
    private void buildModuleConfig(
            String assistantName,
            String prompt,
            String summaryMemory,
            String voice,
            String vadModelId,
            String asrModelId,
            String llmModelId,
            String vllmModelId,
            String ttsModelId,
            String memModelId,
            String intentModelId,
            Map<String, Object> result,
            boolean isCache) {
        Map<String, String> selectedModule = new HashMap<>();

        String[] modelTypes = { "VAD", "ASR", "TTS", "Memory", "Intent", "LLM", "VLLM" };
        String[] modelIds = { vadModelId, asrModelId, ttsModelId, memModelId, intentModelId, llmModelId, vllmModelId };
        String intentLLMModelId = null;
        String memLocalShortLLMModelId = null;

        for (int i = 0; i < modelIds.length; i++) {
            if (modelIds[i] == null) {
                continue;
            }
            ModelConfigEntity model = modelConfigService.getModelById(modelIds[i], isCache);
            Map<String, Object> typeConfig = new HashMap<>();
            if (model.getConfigJson() != null) {
                typeConfig.put(model.getId(), model.getConfigJson());
                // 如果是TTS类型，添加private_voice属性
                if ("TTS".equals(modelTypes[i]) && voice != null) {
                    ((Map<String, Object>) model.getConfigJson()).put("private_voice", voice);
                }
                // 如果是Intent类型，且type=intent_llm，则给他添加附加模型
                if ("Intent".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("intent_llm".equals(map.get("type"))) {
                        intentLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(intentLLMModelId) && intentLLMModelId.equals(llmModelId)) {
                            intentLLMModelId = null;
                        }
                    }
                    if (map.get("functions") != null) {
                        String functionStr = (String) map.get("functions");
                        if (StringUtils.isNotBlank(functionStr)) {
                            String[] functions = functionStr.split("\\;");
                            map.put("functions", functions);
                        }
                    }
                }
                if ("Memory".equals(modelTypes[i])) {
                    Map<String, Object> map = (Map<String, Object>) model.getConfigJson();
                    if ("mem_local_short".equals(map.get("type"))) {
                        memLocalShortLLMModelId = (String) map.get("llm");
                        if (StringUtils.isNotBlank(memLocalShortLLMModelId)
                                && memLocalShortLLMModelId.equals(llmModelId)) {
                            memLocalShortLLMModelId = null;
                        }
                    }
                }
                // 如果是LLM类型，且intentLLMModelId不为空，则添加附加模型
                if ("LLM".equals(modelTypes[i])) {
                    if (StringUtils.isNotBlank(intentLLMModelId)) {
                        if (!typeConfig.containsKey(intentLLMModelId)) {
                            ModelConfigEntity intentLLM = modelConfigService.getModelById(intentLLMModelId, isCache);
                            typeConfig.put(intentLLM.getId(), intentLLM.getConfigJson());
                        }
                    }
                    if (StringUtils.isNotBlank(memLocalShortLLMModelId)) {
                        if (!typeConfig.containsKey(memLocalShortLLMModelId)) {
                            ModelConfigEntity memLocalShortLLM = modelConfigService
                                    .getModelById(memLocalShortLLMModelId, isCache);
                            typeConfig.put(memLocalShortLLM.getId(), memLocalShortLLM.getConfigJson());
                        }
                    }
                }
            }
            result.put(modelTypes[i], typeConfig);

            selectedModule.put(modelTypes[i], model.getId());
        }

        result.put("selected_module", selectedModule);
        if (StringUtils.isNotBlank(prompt)) {
            prompt = prompt.replace("{{assistant_name}}", StringUtils.isBlank(assistantName) ? "小智" : assistantName);
        }
        result.put("prompt", prompt);
        result.put("summaryMemory", summaryMemory);
    }

    /**
     * 添加Token配额相关配置到结果中
     *
     * @param result 结果Map
     */
    private void addTokenQuotaConfig(Map<String, Object> result) {
        // 获取Token配额相关配置，如果不存在则使用默认值
        String enableTokenQuota = sysParamsService.getValue("enable_token_quota", true);
        if (enableTokenQuota == null) {
            enableTokenQuota = "false"; // 默认值
        }
        result.put("enable_token_quota", enableTokenQuota);

        String deviceTokenQuota = sysParamsService.getValue("device_token_quota", true);
        if (deviceTokenQuota == null) {
            deviceTokenQuota = "0"; // 默认值
        }
        result.put("device_token_quota", deviceTokenQuota);

        String accountTokenQuota = sysParamsService.getValue("account_token_quota", true);
        if (accountTokenQuota == null) {
            accountTokenQuota = "0"; // 默认值
        }
        result.put("account_token_quota", accountTokenQuota);
    }

    /**
     * 添加MAC认证相关配置到结果中
     *
     * @param result 结果Map
     */
    private void addMacAuthConfig(Map<String, Object> result) {
        // 获取MAC认证启用状态
        String macAuthEnabledStr = sysParamsService.getValue("mac_auth.enabled", true);
        boolean macAuthEnabled = "true".equalsIgnoreCase(macAuthEnabledStr);
    
        // 获取MAC自动注册状态
        String macAutoRegisterStr = sysParamsService.getValue("mac_auth.auto_register", false);
        boolean macAutoRegister = "true".equalsIgnoreCase(macAutoRegisterStr);
    
        // 创建或获取 mac_auth map
        @SuppressWarnings("unchecked")
        Map<String, Object> macAuth = (Map<String, Object>) result.computeIfAbsent("mac_auth", k -> new HashMap<>());
    
        // 设置值
        macAuth.put("enabled", macAuthEnabled);
        macAuth.put("auto_register", macAutoRegister);
    }

    /**
     * 确保认证配置正确（二选一原则：MAC认证 或 Token认证）
     *
     * @param result 结果Map
     */
    private void ensureAuthConfig(Map<String, Object> result) {
        // 获取MAC认证状态
        Map<String, Object> macAuth = (Map<String, Object>) result.get("mac_auth");
        boolean macAuthEnabled = false;
        if (macAuth != null) {
            macAuthEnabled = Boolean.TRUE.equals(macAuth.get("enabled"));
        }

        // 获取Token认证状态
        String serverAuthEnabled = sysParamsService.getValue("server.auth.enabled", true);
        boolean tokenAuthEnabled = "true".equalsIgnoreCase(serverAuthEnabled);

        // 二选一原则：如果MAC认证禁用，强制启用Token认证
        if (!macAuthEnabled && !tokenAuthEnabled) {
            log.warn("检测到MAC认证和Token认证都被禁用，强制启用Token认证以确保安全");
            // 这里不直接修改数据库，而是在返回的配置中强制设置
            tokenAuthEnabled = true;
        }

        // 构建server.auth配置
        Map<String, Object> server = (Map<String, Object>) result.computeIfAbsent("server", k -> new HashMap<>());
        Map<String, Object> auth = (Map<String, Object>) server.computeIfAbsent("auth", k -> new HashMap<>());
        auth.put("enabled", tokenAuthEnabled);

        log.warn("认证配置状态 - MAC认证: {}, Token认证: {}", macAuthEnabled, tokenAuthEnabled);
    }

    /**
     * 为未注册设备生成绑定码（Token认证模式）
     *
     * @param macAddress MAC地址
     * @return 生成的绑定码
     */
    private String generateBindingCodeForDevice(String macAddress) {
        try {
            // 生成6位随机数字作为绑定码
            String bindingCode = cn.hutool.core.util.RandomUtil.randomNumbers(6);

            // 构建设备数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", macAddress);
            dataMap.put("mac_address", macAddress);
            dataMap.put("board", "unknown");
            dataMap.put("app_version", null);
            dataMap.put("deviceId", macAddress);
            dataMap.put("activation_code", bindingCode);

            // 生成缓存key
            String safeDeviceId = macAddress.replace(":", "_").toLowerCase();
            String dataKey = String.format("ota:activation:data:%s", safeDeviceId);
            String codeKey = "ota:activation:code:" + bindingCode;

            // 写入Redis缓存（设置过期时间为1小时）
            redisUtils.set(dataKey, dataMap, 3600);
            redisUtils.set(codeKey, macAddress, 3600);

            log.info("为设备 {} 生成绑定码: {}", macAddress, bindingCode);
            return bindingCode;
        } catch (Exception e) {
            log.error("为设备 {} 生成绑定码失败", macAddress, e);
            return null;
        }
    }
}
