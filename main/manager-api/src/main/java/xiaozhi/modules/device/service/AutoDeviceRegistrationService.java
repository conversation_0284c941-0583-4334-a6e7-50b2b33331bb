package xiaozhi.modules.device.service;

/**
 * 设备自动注册服务
 * 
 * <AUTHOR>
 * @version 1.0, 2025/5/28
 * @since 1.0.0
 */
public interface AutoDeviceRegistrationService {
    
    /**
     * 为自动注册的MAC地址创建设备记录
     * 
     * @param macAddress MAC地址
     * @param remark 设备备注
     * @return 是否创建成功
     */
    boolean createDeviceForAutoRegisteredMac(String macAddress, String remark);
    
    /**
     * 获取默认智能体模板ID
     * 
     * @return 默认智能体模板ID
     */
    String getDefaultAgentTemplateId();
}
