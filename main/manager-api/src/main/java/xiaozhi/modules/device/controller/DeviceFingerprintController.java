package xiaozhi.modules.device.controller;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.common.validator.group.AddGroup;
import xiaozhi.common.validator.group.DefaultGroup;
import xiaozhi.common.validator.group.UpdateGroup;
import xiaozhi.modules.device.dto.DeviceFingerprintDTO;
import xiaozhi.modules.device.service.DeviceFingerprintService;

/**
 * 设备指纹管理
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@RestController
@RequestMapping("device/fingerprint")
@Tag(name = "设备指纹管理")
@AllArgsConstructor
public class DeviceFingerprintController {
    
    private final DeviceFingerprintService deviceFingerprintService;
    
    @GetMapping("{macAddress}")
    @Operation(summary = "获取设备指纹")
    public Result<DeviceFingerprintDTO> getByMacAddress(@PathVariable("macAddress") String macAddress) {
        DeviceFingerprintDTO data = deviceFingerprintService.getByMacAddress(macAddress);
        return new Result<DeviceFingerprintDTO>().ok(data);
    }
    
    @PostMapping
    @Operation(summary = "保存设备指纹")
    @LogOperation("保存设备指纹")
    public Result<Void> save(@RequestBody DeviceFingerprintDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        
        deviceFingerprintService.save(dto);
        
        return new Result<>();
    }
    
    @PutMapping
    @Operation(summary = "修改设备指纹")
    @LogOperation("修改设备指纹")
    @RequiresPermissions("sys:role:admin")
    public Result<Void> update(@RequestBody DeviceFingerprintDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);
        
        deviceFingerprintService.update(dto);
        
        return new Result<>();
    }
    
    @DeleteMapping("{id}")
    @Operation(summary = "删除设备指纹")
    @LogOperation("删除设备指纹")
    @RequiresPermissions("sys:role:admin")
    public Result<Void> delete(@PathVariable("id") Long id) {
        deviceFingerprintService.delete(id);
        
        return new Result<>();
    }
}
