package xiaozhi.modules.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.modules.agent.dao.AgentTemplateDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.agent.entity.AgentTemplateEntity;
import xiaozhi.modules.agent.service.AgentService;
import xiaozhi.modules.device.dao.DeviceDao;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.AutoDeviceRegistrationService;

import java.util.Date;
import java.util.UUID;

/**
 * 设备自动注册服务实现
 * 
 * <AUTHOR>
 * @version 1.0, 2025/5/28
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class AutoDeviceRegistrationServiceImpl implements AutoDeviceRegistrationService {
    
    private final DeviceDao deviceDao;
    private final AgentTemplateDao agentTemplateDao;
    private final AgentService agentService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDeviceForAutoRegisteredMac(String macAddress, String remark) {
        try {
            // 检查设备是否已存在
            QueryWrapper<DeviceEntity> deviceWrapper = new QueryWrapper<>();
            deviceWrapper.eq("mac_address", macAddress);
            Long existingDeviceCount = deviceDao.selectCount(deviceWrapper);
            if (existingDeviceCount > 0) {
                log.info("设备 {} 已存在于设备列表中", macAddress);
                return true; // 已存在，返回成功
            }

            // 获取默认智能体ID（修复：应该使用智能体而不是模板）
            String defaultAgentId = getDefaultAgentId();
            if (StringUtils.isBlank(defaultAgentId)) {
                log.warn("未找到默认智能体，无法创建设备记录: {}", macAddress);
                return false;
            }

            // 创建设备记录
            Date now = new Date();
            DeviceEntity deviceEntity = new DeviceEntity();
            deviceEntity.setId(generateDeviceId());
            deviceEntity.setMacAddress(macAddress);
            deviceEntity.setAgentId(defaultAgentId);
            deviceEntity.setAlias(remark != null ? remark : "自动注册设备");
            deviceEntity.setLastConnectedAt(now);
            deviceEntity.setStatus(1); // 设备状态：启用
            deviceEntity.setRegisterSource("auto"); // 注册来源：自动注册
            deviceEntity.setRegisterTime(now); // 注册时间
            deviceEntity.setAutoUpdate(1); // 自动更新：启用
            deviceEntity.setSort(0);
            deviceEntity.setCreateDate(now);
            deviceEntity.setUpdateDate(now);

            // 插入设备记录
            int result = deviceDao.insert(deviceEntity);
            
            if (result > 0) {
                log.info("自动注册设备 {} 成功创建设备记录，绑定到默认智能体: {}", macAddress, defaultAgentId);
                return true;
            } else {
                log.warn("自动注册设备 {} 创建设备记录失败", macAddress);
                return false;
            }

        } catch (Exception e) {
            log.error("为自动注册设备创建设备记录失败: {}", macAddress, e);
            return false;
        }
    }
    
    /**
     * 获取默认智能体ID
     * 修复：应该返回ai_agent表中的默认智能体ID，而不是模板ID
     */
    public String getDefaultAgentId() {
        try {
            AgentEntity defaultAgent = agentService.getOfficialDefaultAgent();
            if (defaultAgent != null) {
                log.debug("获取到默认智能体: {} ({})", defaultAgent.getAgentName(), defaultAgent.getId());
                return defaultAgent.getId();
            } else {
                log.warn("未找到默认智能体，尝试创建");
                defaultAgent = agentService.createOfficialDefaultAgent();
                return defaultAgent != null ? defaultAgent.getId() : null;
            }
        } catch (Exception e) {
            log.error("获取默认智能体ID失败", e);
            return null;
        }
    }

    @Override
    @Deprecated
    public String getDefaultAgentTemplateId() {
        log.warn("getDefaultAgentTemplateId方法已废弃，请使用getDefaultAgentId方法");
        try {
            // 查询sort=1的默认智能体模板
            QueryWrapper<AgentTemplateEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("sort", 1);
            wrapper.last("LIMIT 1");

            AgentTemplateEntity defaultTemplate = agentTemplateDao.selectOne(wrapper);
            if (defaultTemplate != null) {
                log.debug("获取到默认智能体模板: {} ({})", defaultTemplate.getAgentName(), defaultTemplate.getId());
                return defaultTemplate.getId();
            } else {
                log.warn("未找到默认智能体模板 (sort=1)");
                return null;
            }
        } catch (Exception e) {
            log.error("获取默认智能体模板ID失败", e);
            return null;
        }
    }
    
    /**
     * 生成设备ID
     */
    private String generateDeviceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
