package xiaozhi.modules.device.service;

import xiaozhi.common.service.BaseService;
import xiaozhi.modules.device.dto.DeviceFingerprintDTO;
import xiaozhi.modules.device.entity.DeviceFingerprintEntity;

/**
 * 设备指纹服务接口
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
public interface DeviceFingerprintService extends BaseService<DeviceFingerprintEntity> {
    
    /**
     * 根据MAC地址获取设备指纹
     * 
     * @param macAddress MAC地址
     * @return 设备指纹
     */
    DeviceFingerprintDTO getByMacAddress(String macAddress);
    
    /**
     * 保存设备指纹
     * 
     * @param dto 设备指纹DTO
     */
    void save(DeviceFingerprintDTO dto);
    
    /**
     * 更新设备指纹
     * 
     * @param dto 设备指纹DTO
     */
    void update(DeviceFingerprintDTO dto);
    
    /**
     * 删除设备指纹
     * 
     * @param id ID
     */
    void delete(Long id);
    
    /**
     * 检查设备指纹是否存在
     * 
     * @param macAddress MAC地址
     * @return 是否存在
     */
    boolean checkFingerprintExists(String macAddress);
}
