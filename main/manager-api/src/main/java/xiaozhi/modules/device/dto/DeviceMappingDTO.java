package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备-智能体映射DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@Schema(description = "设备-智能体映射")
public class DeviceMappingDTO {
    
    @Schema(description = "MAC地址")
    private String macAddress;
    
    @Schema(description = "智能体ID")
    private String agentId;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "设备别名")
    private String alias;
    
    @Schema(description = "设备状态")
    private Integer status;
} 