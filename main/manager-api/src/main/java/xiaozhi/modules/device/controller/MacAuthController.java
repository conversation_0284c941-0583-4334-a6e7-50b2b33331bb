package xiaozhi.modules.device.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.Result;

import xiaozhi.modules.quota.dao.MacBlacklistDao;
import xiaozhi.modules.sys.service.SysParamsService;
import xiaozhi.modules.device.service.DeviceService;
import java.util.List;

/**
 * MAC地址认证控制器
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@RestController
@RequestMapping("device/auth")
@Tag(name = "MAC地址认证")
@AllArgsConstructor
public class MacAuthController {


    private final MacBlacklistDao macBlacklistDao;
    private final RedisUtils redisUtils;
    private final SysParamsService sysParamsService;
    private final DeviceService deviceService;

    // 缓存过期时间（秒）
    private static final int CACHE_TTL = 300; // 5分钟

    @GetMapping("/check/{macAddress}")
    @Operation(summary = "检查MAC地址是否有效")
    public Result<Boolean> checkMacAddress(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Boolean>().ok(false);
        }

        // 检查MAC地址格式
        if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
            return new Result<Boolean>().ok(false);
        }

        // 尝试从Redis缓存获取结果
        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        Object cachedResult = redisUtils.get(cacheKey);

        if (cachedResult != null) {
            if ("blacklisted".equals(cachedResult)) {
                return new Result<Boolean>().error("MAC地址已被禁用");
            } else {
                return new Result<Boolean>().ok(Boolean.valueOf(cachedResult.toString()));
            }
        }

        // 缓存未命中，查询数据库

        // 检查是否在黑名单中
        if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
            // 缓存黑名单结果
            redisUtils.set(cacheKey, "blacklisted", CACHE_TTL);
            return new Result<Boolean>().error("MAC地址已被禁用");
        }

        // 检查是否在设备表中且已启用
        boolean isEnabled = deviceService.isDeviceEnabled(macAddress);

        // 缓存结果
        redisUtils.set(cacheKey, isEnabled, CACHE_TTL);

        return new Result<Boolean>().ok(isEnabled);
    }

    @PostMapping("/authenticate")
    @Operation(summary = "统一MAC地址认证（包含格式验证、频率限制、自动注册）")
    public Result<MacAuthResult> authenticateMacAddress(@RequestBody MacAuthRequest request) {
        String macAddress = request.getMacAddress();
        String clientId = request.getClientId();

        // 1. 基础验证
        if (StringUtils.isBlank(macAddress)) {
            return new Result<MacAuthResult>().error("MAC address cannot be empty");
        }

        // 2. MAC地址格式验证
        if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
            return new Result<MacAuthResult>().error("Invalid MAC address format");
        }

        // 3. 访问频率限制检查
        if (!checkAccessRate(macAddress)) {
            return new Result<MacAuthResult>().error("Access rate too high, please try again later");
        }

        // 4. 检查是否在黑名单中
        if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
            return new Result<MacAuthResult>().error("MAC address has been disabled");
        }

        // 5. 检查是否在设备表中且已启用（替代白名单逻辑）
        boolean deviceEnabled = deviceService.isDeviceEnabled(macAddress);

        if (deviceEnabled) {
            // 设备已注册且启用，直接认证成功
            MacAuthResult result = new MacAuthResult();
            result.setAuthenticated(true);
            result.setMethod("device_enabled");
            result.setMacAddress(macAddress);
            return new Result<MacAuthResult>().ok(result);
        }

        // 6. 检查是否启用自动注册（统一使用mac_auth.auto_register）
        String autoRegisterParam = sysParamsService.getValue("mac_auth.auto_register", true);
        boolean autoRegisterEnabled = "true".equalsIgnoreCase(autoRegisterParam);

        if (autoRegisterEnabled) {
            // 自动创建设备记录并绑定到默认智能体
            boolean registered = deviceService.bindDeviceToDefaultAgent(macAddress);
            if (registered) {
                // 自动注册成功后，立即清理MAC认证缓存，确保下次认证时重新查询数据库
                String cacheKey = RedisKeys.getMacAuthKey(macAddress);
                redisUtils.delete(cacheKey);

                MacAuthResult result = new MacAuthResult();
                result.setAuthenticated(true);
                result.setMethod("auto_register");
                result.setMacAddress(macAddress);
                return new Result<MacAuthResult>().ok(result);
            } else {
                return new Result<MacAuthResult>().error("Auto registration failed");
            }
        }

        // 7. 认证失败
        return new Result<MacAuthResult>().error("MAC address unauthorized");
    }

    /**
     * 检查访问频率限制
     */
    private boolean checkAccessRate(String macAddress) {
        String rateLimitKey = "mac:rate_limit:" + macAddress;
        String maxAccessParam = sysParamsService.getValue("mac_auth.access_limit", true);
        int maxAccess = StringUtils.isNotBlank(maxAccessParam) ? Integer.parseInt(maxAccessParam) : 10;

        // 获取当前访问次数
        Object currentCount = redisUtils.get(rateLimitKey);
        int count = currentCount != null ? Integer.parseInt(currentCount.toString()) : 0;

        if (count >= maxAccess) {
            return false;
        }

        // 增加访问次数，设置1分钟过期
        redisUtils.set(rateLimitKey, count + 1, 60);
        return true;
    }

    /**
     * MAC认证请求对象
     */
    public static class MacAuthRequest {
        private String macAddress;
        private String clientId;

        // getters and setters
        public String getMacAddress() { return macAddress; }
        public void setMacAddress(String macAddress) { this.macAddress = macAddress; }
        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }
    }

    /**
     * MAC认证结果对象
     */
    public static class MacAuthResult {
        private boolean authenticated;
        private String method; // "mac_whitelist" 或 "auto_register"
        private String macAddress;

        // getters and setters
        public boolean isAuthenticated() { return authenticated; }
        public void setAuthenticated(boolean authenticated) { this.authenticated = authenticated; }
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        public String getMacAddress() { return macAddress; }
        public void setMacAddress(String macAddress) { this.macAddress = macAddress; }
    }

    @GetMapping("/registered")
    @Operation(summary = "获取所有已注册设备的MAC地址列表")
    public Result<List<String>> getRegisteredMacAddresses() {
        List<String> macAddresses = deviceService.getAllRegisteredMacAddresses();
        return new Result<List<String>>().ok(macAddresses);
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取所有启用设备的MAC地址列表")
    public Result<List<String>> getEnabledMacAddresses() {
        List<String> macAddresses = deviceService.getAllEnabledMacAddresses();
        return new Result<List<String>>().ok(macAddresses);
    }

    @GetMapping("/invalidate/{macAddress}")
    @Operation(summary = "使MAC地址缓存失效")
    public Result<Void> invalidateCache(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Void>().error("MAC address cannot be empty");
        }

        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        redisUtils.delete(cacheKey);

        return new Result<>();
    }
}
