package xiaozhi.modules.device.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.device.entity.DeviceFingerprintEntity;

/**
 * 设备指纹DAO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Mapper
public interface DeviceFingerprintDao extends BaseMapper<DeviceFingerprintEntity> {
    
    /**
     * 根据MAC地址获取设备指纹
     * 
     * @param macAddress MAC地址
     * @return 设备指纹
     */
    @Select("SELECT * FROM ai_device_fingerprint WHERE mac_address = #{macAddress} AND status = 1")
    DeviceFingerprintEntity getByMacAddress(String macAddress);
    
    /**
     * 检查设备指纹是否存在
     * 
     * @param macAddress MAC地址
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) FROM ai_device_fingerprint WHERE mac_address = #{macAddress}")
    int checkFingerprintExists(String macAddress);
}
