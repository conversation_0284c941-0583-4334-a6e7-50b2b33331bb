package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 设备切换智能体DTO
 */
@Data
@Schema(description = "设备切换智能体")
public class DeviceSwitchAgentDTO {

    @Schema(description = "设备MAC地址")
    @NotBlank(message = "设备MAC地址不能为空")
    private String deviceMac;

    @Schema(description = "新智能体ID")
    @NotBlank(message = "新智能体ID不能为空")
    private String newAgentId;
}
