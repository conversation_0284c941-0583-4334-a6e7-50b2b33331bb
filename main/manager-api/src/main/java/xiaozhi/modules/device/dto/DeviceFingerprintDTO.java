package xiaozhi.modules.device.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 设备指纹DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@Schema(description = "设备指纹")
public class DeviceFingerprintDTO {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "MAC地址")
    @NotBlank(message = "MAC地址不能为空")
    @Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "MAC地址格式不正确")
    private String macAddress;
    
    @Schema(description = "设备型号")
    private String deviceModel;
    
    @Schema(description = "固件版本")
    private String firmwareVersion;
    
    @Schema(description = "芯片ID")
    private String chipId;
    
    @Schema(description = "最后连接IP")
    private String lastIp;
    
    @Schema(description = "状态(0禁用/1启用)")
    private Integer status;
}
