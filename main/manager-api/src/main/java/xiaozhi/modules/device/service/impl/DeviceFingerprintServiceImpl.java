package xiaozhi.modules.device.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.device.dao.DeviceFingerprintDao;
import xiaozhi.modules.device.dto.DeviceFingerprintDTO;
import xiaozhi.modules.device.entity.DeviceFingerprintEntity;
import xiaozhi.modules.device.service.DeviceFingerprintService;

import java.util.Date;

/**
 * 设备指纹服务实现
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Service
public class DeviceFingerprintServiceImpl extends BaseServiceImpl<DeviceFingerprintDao, DeviceFingerprintEntity> implements DeviceFingerprintService {
    
    @Override
    public DeviceFingerprintDTO getByMacAddress(String macAddress) {
        DeviceFingerprintEntity entity = baseDao.getByMacAddress(macAddress);
        return ConvertUtils.sourceToTarget(entity, DeviceFingerprintDTO.class);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DeviceFingerprintDTO dto) {
        DeviceFingerprintEntity entity = ConvertUtils.sourceToTarget(dto, DeviceFingerprintEntity.class);
        entity.setCreateDate(new Date());
        entity.setStatus(1);
        insert(entity);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeviceFingerprintDTO dto) {
        DeviceFingerprintEntity entity = ConvertUtils.sourceToTarget(dto, DeviceFingerprintEntity.class);
        entity.setUpdateDate(new Date());
        updateById(entity);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        deleteById(id);
    }
    
    @Override
    public boolean checkFingerprintExists(String macAddress) {
        return baseDao.checkFingerprintExists(macAddress) > 0;
    }
}
