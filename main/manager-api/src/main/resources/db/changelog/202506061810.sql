-- 架构优化：智能体表合并，简化系统架构
-- 执行时间：2025-06-06 18:09:00
-- 说明：
-- 1. 将ai_default_agent表合并到ai_agent表中
-- 2. 使用agent_type字段区分用户智能体和默认智能体
-- 3. 修复必要的模型配置问题

-- =============================================================================
-- 第一部分：智能体表合并
-- =============================================================================

-- 1. 为 ai_agent 表添加新字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = 'ai_agent' AND column_name = 'agent_type') = 0,
    'ALTER TABLE ai_agent ADD COLUMN agent_type ENUM(''user'', ''default'') DEFAULT ''user'' COMMENT ''智能体类型''',
    'SELECT "agent_type column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = 'ai_agent' AND column_name = 'is_active') = 0,
    'ALTER TABLE ai_agent ADD COLUMN is_active BOOLEAN DEFAULT TRUE COMMENT ''是否激活（仅对默认智能体有效）''',
    'SELECT "is_active column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建索引（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_name = 'ai_agent' AND index_name = 'idx_agent_type') = 0,
    'CREATE INDEX idx_agent_type ON ai_agent(agent_type)',
    'SELECT "idx_agent_type index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_name = 'ai_agent' AND index_name = 'idx_is_active') = 0,
    'CREATE INDEX idx_is_active ON ai_agent(is_active)',
    'SELECT "idx_is_active index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 迁移默认智能体数据（如果ai_default_agent表存在）
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'ai_default_agent');
SET @sql = IF(@table_exists > 0,
    'INSERT IGNORE INTO ai_agent (
        id, agent_code, agent_name, asr_model_id, vad_model_id,
        llm_model_id, vllm_model_id, tts_model_id, tts_voice_id,
        mem_model_id, intent_model_id, chat_history_conf,
        system_prompt, summary_memory, lang_code, language,
        sort, agent_type, is_active,
        creator, created_at, updater, updated_at
    )
    SELECT
        CASE
            WHEN da.id IN (SELECT id FROM ai_agent)
            THEN CONCAT(''DEFAULT_'', da.id)
            ELSE da.id
        END as id,
        da.agent_code, da.agent_name, da.asr_model_id, da.vad_model_id,
        da.llm_model_id, da.vllm_model_id, da.tts_model_id, da.tts_voice_id,
        da.mem_model_id, da.intent_model_id, da.chat_history_conf,
        da.system_prompt, da.summary_memory, da.lang_code, da.language,
        da.sort, ''default'' as agent_type, da.is_active,
        da.creator, da.created_at, da.updater, da.updated_at
    FROM ai_default_agent da',
    'SELECT "ai_default_agent table does not exist, skipping migration" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 更新现有用户智能体的字段
UPDATE ai_agent
SET agent_type = 'user'
WHERE user_id IS NOT NULL AND (agent_type IS NULL OR agent_type = '');

-- 5. 创建默认智能体（如果不存在）
INSERT IGNORE INTO ai_agent (
    id, agent_code, agent_name,
    asr_model_id, vad_model_id, llm_model_id, vllm_model_id, tts_model_id,
    tts_voice_id, mem_model_id, intent_model_id, system_prompt,
    chat_history_conf, summary_memory, lang_code, language, sort,
    agent_type, is_active, creator, created_at
) VALUES (
    'OFFICIAL_DEFAULT_AGENT',
    'nous_ai',
    'nous ai',
    'ASR_FunASR',
    'VAD_SileroVAD',
    'LLM_DeepSeekLLM',
    NULL,
    'TTS_EdgeTTS',
    'TTS_EdgeTTS0001',
    'Memory_mem_local_short',
    'Intent_nointent',
    '[角色设定]\n你是一个叫{{assistant_name}}的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话',
    2,
    NULL,
    'zh',
    '中文',
    0,
    'default',  -- 设置为默认智能体
    1,  -- 设置为激活状态
    1,
    NOW()
);

-- 6. 更新设备绑定关系（处理ID冲突的情况）
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'ai_default_agent');
SET @sql = IF(@table_exists > 0,
    'UPDATE ai_device d
     SET agent_id = CONCAT(''DEFAULT_'', d.agent_id)
     WHERE d.agent_id IN (
         SELECT da.id FROM ai_default_agent da
         WHERE da.id IN (SELECT id FROM ai_agent WHERE user_id IS NOT NULL)
     )',
    'SELECT "ai_default_agent table does not exist, skipping device update" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 备份并删除原表（如果存在）
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'ai_default_agent');
SET @sql = IF(@table_exists > 0, 'RENAME TABLE ai_default_agent TO ai_default_agent_backup', 'SELECT "ai_default_agent table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 第二部分：模型配置修复
-- =============================================================================

-- 1. 修复用户智能体中的模型配置（如果有使用不存在的模型ID）
UPDATE ai_agent
SET
    vad_model_id = CASE
        WHEN vad_model_id = 'silero_vad' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'silero_vad')
        THEN 'VAD_SileroVAD'
        ELSE vad_model_id
    END,
    asr_model_id = CASE
        WHEN asr_model_id = 'sense_voice_small' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'sense_voice_small')
        THEN 'ASR_FunASR'
        ELSE asr_model_id
    END,
    llm_model_id = CASE
        WHEN llm_model_id = 'deepseek_chat' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'deepseek_chat')
        THEN 'LLM_DeepSeekLLM'
        ELSE llm_model_id
    END,
    tts_model_id = CASE
        WHEN tts_model_id = 'edge_tts' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'edge_tts')
        THEN 'TTS_EdgeTTS'
        ELSE tts_model_id
    END
WHERE user_id IS NOT NULL  -- 只修复用户智能体
  AND (vad_model_id = 'silero_vad'
       OR asr_model_id = 'sense_voice_small'
       OR llm_model_id = 'deepseek_chat'
       OR tts_model_id = 'edge_tts');

-- 2. 修复智能体模板配置
UPDATE ai_agent_template
SET
    vad_model_id = CASE
        WHEN vad_model_id = 'silero_vad' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'silero_vad')
        THEN 'VAD_SileroVAD'
        ELSE vad_model_id
    END,
    asr_model_id = CASE
        WHEN asr_model_id = 'sense_voice_small' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'sense_voice_small')
        THEN 'ASR_FunASR'
        ELSE asr_model_id
    END,
    llm_model_id = CASE
        WHEN llm_model_id = 'deepseek_chat' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'deepseek_chat')
        THEN 'LLM_DeepSeekLLM'
        ELSE llm_model_id
    END,
    tts_model_id = CASE
        WHEN tts_model_id = 'edge_tts' AND NOT EXISTS (SELECT 1 FROM ai_model_config WHERE id = 'edge_tts')
        THEN 'TTS_EdgeTTS'
        ELSE tts_model_id
    END
WHERE vad_model_id = 'silero_vad'
   OR asr_model_id = 'sense_voice_small'
   OR llm_model_id = 'deepseek_chat'
   OR tts_model_id = 'edge_tts';

-- =============================================================================
-- 第三部分：设备关联逻辑优化
-- =============================================================================

-- 1. 更新设备表，确保MAC验证的设备关联到默认智能体
UPDATE ai_device
SET agent_id = 'OFFICIAL_DEFAULT_AGENT'
WHERE (agent_id IS NULL OR agent_id = '')
  AND user_id IS NULL;  -- MAC验证的设备通常user_id为NULL

-- =============================================================================
-- 第四部分：系统参数和完成标记
-- =============================================================================

-- 1. 添加系统参数
INSERT IGNORE INTO sys_params (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('agent_table_merge.enabled', 'true', 'boolean', 1, '智能体表合并功能已启用', 1, NOW()),
('agent_table_merge.completed', 'true', 'boolean', 1, '智能体表合并完成标记', 1, NOW());

-- 2. 清理Redis缓存（通过更新时间戳触发缓存失效）
UPDATE ai_model_config
SET update_date = NOW()
WHERE id IN ('TTS_EdgeTTS', 'ASR_FunASR', 'VAD_SileroVAD', 'LLM_DeepSeekLLM', 'LLM_ChatGLMLLM');

-- 3. 插入完成标记
INSERT IGNORE INTO sys_params (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('AGENT_TABLE_MERGE_COMPLETED', '2025-06-06', 'string', 1, '智能体表合并完成：统一使用ai_agent表，通过agent_type区分类型', 1, NOW()),
('AGENT_ARCHITECTURE_SIMPLIFIED', '2025-06-06', 'string', 1, '智能体架构简化完成：删除分表复杂性，统一查询逻辑', 1, NOW()),
('AGENT_TYPE_FIELD_ADDED', '2025-06-06', 'string', 1, 'agent_type字段已添加到ai_agent表，用于区分用户智能体和默认智能体', 1, NOW());

-- =============================================================================
-- 完成
-- =============================================================================
