-- 添加缺失的系统参数
-- 本文件用于添加MAC鉴权、Token配额等重要的系统参数
-- ID范围：2000-2099，避免与现有参数冲突
-- 参数名格式遵循Python端使用的点分割格式

-- =============================================================================
-- 第一部分：MAC鉴权相关参数（Python端实际使用的参数名）
-- =============================================================================

-- MAC鉴权配置（统一使用结构化参数名称）
INSERT IGNORE INTO `sys_params` (id, param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
(2001, 'mac_auth.enabled', 'true', 'boolean', 1, '是否启用MAC地址认证', 1, NOW()),
(2002, 'mac_auth.auto_register', 'false', 'boolean', 1, '是否启用MAC地址自动注册', 1, NOW()),
(2030, 'mac_auth.enable_device_fingerprint', 'false', 'boolean', 1, '是否启用设备指纹验证', 1, NOW()),
(2031, 'mac_auth.access_limit', '10', 'number', 1, 'MAC地址每分钟最大访问次数', 1, NOW()),
(2032, 'mac_auth.cache_ttl', '86400', 'number', 1, 'MAC认证缓存有效期（秒）', 1, NOW()),
(2033, 'mac_auth.cache_retry_interval', '300', 'number', 1, 'MAC认证缓存重试间隔（秒）', 1, NOW()),
(2034, 'mac_auth.max_retry_attempts', '3', 'number', 1, 'MAC认证最大重试次数', 1, NOW());

-- =============================================================================
-- 第二部分：Token配额相关参数（Python端实际使用的参数名）
-- =============================================================================

-- Token配额系统参数（基于connection.py中实际使用的参数）
INSERT IGNORE INTO `sys_params` (id, param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
(2010, 'enable_token_quota', 'true', 'boolean', 1, '是否启用Token配额限制', 1, NOW()),
(2011, 'device_token_quota', '10000', 'number', 1, '设备每日Token配额（默认智能体）', 1, NOW()),
(2012, 'account_token_quota', '100000', 'number', 1, '账号每日Token配额（用户智能体）', 1, NOW());

-- =============================================================================
-- 第三部分：认证系统参数（确保二选一原则）
-- =============================================================================

-- Token认证配置（与MAC认证二选一）
INSERT IGNORE INTO `sys_params` (id, param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
(2020, 'server.auth.enabled', 'true', 'boolean', 1, 'Token认证是否启用（MAC认证禁用时的备选方案）', 1, NOW());

-- =============================================================================
-- 第四部分：清理重复参数
-- =============================================================================

-- 删除重复的兼容性参数（如果存在）
DELETE FROM `sys_params` WHERE param_code IN ('mac_access_limit', 'mac_auto_register_enabled');

-- =============================================================================