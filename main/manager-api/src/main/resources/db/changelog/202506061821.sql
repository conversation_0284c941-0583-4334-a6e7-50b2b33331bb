-- 创建配额管理相关表
-- 执行时间：2026-06-05 14:55:00
-- 说明：重新创建配额设置表和配额使用记录表，支持token配额管理

-- =============================================================================
-- 第一部分：创建配额设置表
-- =============================================================================

-- 删除可能存在的表，重新创建
DROP TABLE IF EXISTS `ai_quota_settings`;

-- 创建配额设置表
CREATE TABLE `ai_quota_settings` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `quota_type` VARCHAR(20) NOT NULL COMMENT '配额类型(device/account)',
    `quota_value` INT NOT NULL DEFAULT 0 COMMENT '配额值(0表示不限制)',
    `reset_type` VARCHAR(20) NOT NULL COMMENT '重置类型(daily/monthly/never)',
    `description` VARCHAR(255) COMMENT '配额描述',
    `creator` BIGINT COMMENT '创建者',
    `create_date` DATETIME COMMENT '创建时间',
    `updater` BIGINT COMMENT '更新者',
    `update_date` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_quota_type` (`quota_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额设置表';

-- =============================================================================
-- 第二部分：创建配额使用记录表
-- =============================================================================

-- 删除可能存在的表，重新创建
DROP TABLE IF EXISTS `ai_quota_usage`;

-- 创建配额使用记录表
CREATE TABLE `ai_quota_usage` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT COMMENT '用户ID - 逻辑外键关联sys_user表的id字段',
    `device_mac` VARCHAR(50) COMMENT '设备MAC地址 - 逻辑外键关联ai_device表的mac_address字段',
    `agent_id` VARCHAR(32) COMMENT '智能体ID - 逻辑外键关联ai_agent表的id字段',
    `usage_type` VARCHAR(20) COMMENT '使用类型(input/output)',
    `usage_value` INT DEFAULT 0 COMMENT '使用量值(兼容旧版本字段)',
    `input_tokens` INT DEFAULT 0 COMMENT '输入Token数量',
    `output_tokens` INT DEFAULT 0 COMMENT '输出Token数量',
    `total_tokens` INT DEFAULT 0 COMMENT 'Token总量',
    `model_name` VARCHAR(100) COMMENT '模型名称',
    `usage_date` DATE COMMENT '使用日期',
    `create_date` DATETIME COMMENT '创建时间',
    `creator` BIGINT COMMENT '创建者',
    `update_date` DATETIME COMMENT '更新时间',
    `updater` BIGINT COMMENT '更新者',
    PRIMARY KEY (`id`),
    INDEX `idx_quota_usage_user_date` (`user_id`, `usage_date`) COMMENT '用户日期索引',
    INDEX `idx_quota_usage_device_date` (`device_mac`, `usage_date`) COMMENT '设备日期索引',
    INDEX `idx_quota_usage_agent_date` (`agent_id`, `usage_date`) COMMENT '智能体日期索引',
    INDEX `idx_quota_usage_create_date` (`create_date`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额使用记录表';

-- =============================================================================
-- 第三部分：插入默认配额设置
-- =============================================================================

-- 插入默认配额设置
INSERT INTO `ai_quota_settings` (`quota_type`, `quota_value`, `reset_type`, `description`, `creator`, `create_date`) VALUES
('device', 10000, 'daily', '设备每日默认token配额', 1, NOW()),
('account', 100000, 'daily', '账户每日默认token配额', 1, NOW());

-- 插入测试配额使用记录（可选，用于测试）
-- INSERT INTO `ai_quota_usage` (`device_mac`, `input_tokens`, `output_tokens`, `total_tokens`, `model_name`, `usage_date`, `create_date`) VALUES
-- ('24:0A:C4:1D:3B:F0', 100, 200, 300, 'gpt-3.5-turbo', CURDATE(), NOW()),
-- ('24:0A:C4:1D:3B:F1', 150, 250, 400, 'gpt-4', CURDATE(), NOW());

-- =============================================================================
-- 第四部分：添加系统参数
-- =============================================================================

-- 添加token配额相关的系统参数
INSERT IGNORE INTO `sys_params` (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('enable_token_quota', 'true', 'boolean', 1, '是否启用token配额限制', 1, NOW()),
('device_token_quota', '10000', 'number', 1, '设备每日默认token配额', 1, NOW()),
('account_token_quota', '100000', 'number', 1, '账户每日默认token配额', 1, NOW()),
('token_quota_reset_type', 'daily', 'string', 1, 'token配额重置类型(daily/monthly/never)', 1, NOW());

-- =============================================================================
-- 第五部分：创建配额累计视图（可选）
-- =============================================================================

-- 创建账号Token使用量累计视图
CREATE OR REPLACE VIEW `v_account_token_usage` AS
SELECT 
    a.user_id,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.user_id IS NOT NULL  -- 排除默认智能体
GROUP BY a.user_id, qu.usage_date;

-- 创建设备Token使用量累计视图
CREATE OR REPLACE VIEW `v_device_token_usage` AS
SELECT 
    qu.device_mac,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens
FROM ai_quota_usage qu
WHERE qu.device_mac IS NOT NULL
GROUP BY qu.device_mac, qu.usage_date;

-- =============================================================================
-- 第六部分：完成标记
-- =============================================================================

-- 插入Token配额相关的系统参数
INSERT IGNORE INTO `sys_params` (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('enable_token_quota', 'false', 'boolean', 1, '是否启用Token配额限制', 1, NOW()),
('device_token_quota', '10000', 'number', 1, '设备每日Token配额（默认智能体）', 1, NOW()),
('account_token_quota', '100000', 'number', 1, '账号每日Token配额（用户智能体）', 1, NOW());

-- 插入完成标记
INSERT IGNORE INTO `sys_params` (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('QUOTA_TABLES_CREATED', '2025-01-04', 'string', 1, '配额管理表创建完成标记', 1, NOW()),
('TOKEN_QUOTA_SYSTEM_ENABLED', 'true', 'boolean', 1, 'Token配额系统启用标记', 1, NOW());

-- =============================================================================
-- 完成
-- =============================================================================
