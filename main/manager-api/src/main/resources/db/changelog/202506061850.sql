-- 添加设备状态字段
-- 执行时间：2025-06-06 18:50:00
-- 说明：为ai_device表添加status字段，用于控制设备的启用/禁用状态

-- =============================================================================
-- 添加status字段到ai_device表
-- =============================================================================

-- 检查字段是否已存在，如果不存在则添加
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = 'ai_device' AND column_name = 'status') = 0,
    'ALTER TABLE ai_device ADD COLUMN status TINYINT(1) DEFAULT 1 COMMENT ''设备状态(0禁用/1启用)'' AFTER mac_address',
    'SELECT "status column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 同时添加register_source字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = 'ai_device' AND column_name = 'register_source') = 0,
    'ALTER TABLE ai_device ADD COLUMN register_source VARCHAR(20) DEFAULT ''manual'' COMMENT ''注册来源(manual:手动导入, auto:自动注册, batch:批量导入, activation:激活码)'' AFTER status',
    'SELECT "register_source column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 同时添加register_time字段（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = 'ai_device' AND column_name = 'register_time') = 0,
    'ALTER TABLE ai_device ADD COLUMN register_time DATETIME COMMENT ''注册时间'' AFTER register_source',
    'SELECT "register_time column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 初始化现有数据
-- =============================================================================

-- 将所有现有设备的状态设置为启用
UPDATE ai_device SET status = 1 WHERE status IS NULL;

-- 将所有现有设备的注册来源设置为手动导入（如果为空）
UPDATE ai_device SET register_source = 'manual' WHERE register_source IS NULL OR register_source = '';

-- 将所有现有设备的注册时间设置为创建时间（如果为空）
UPDATE ai_device SET register_time = create_date WHERE register_time IS NULL;

-- =============================================================================
-- 创建索引
-- =============================================================================

-- 为status字段创建索引（如果不存在）
SET @sql = IF(
    (SELECT COUNT(*) FROM information_schema.statistics
     WHERE table_name = 'ai_device' AND index_name = 'idx_device_status') = 0,
    'CREATE INDEX idx_device_status ON ai_device(status)',
    'SELECT "idx_device_status index already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 完成标记
-- =============================================================================

-- 添加完成标记
INSERT IGNORE INTO sys_params (param_code, param_value, value_type, param_type, remark, creator, create_date) VALUES
('device_status_field_added', 'true', 'boolean', 1, '设备状态字段已添加到ai_device表', 1, NOW());
