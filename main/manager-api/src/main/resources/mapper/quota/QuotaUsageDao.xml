<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xiaozhi.modules.quota.dao.QuotaUsageDao">

    <select id="page" resultType="xiaozhi.modules.quota.dto.QuotaUsageDTO">
        SELECT
            q.id,
            q.user_id,
            q.device_mac,
            q.agent_id,
            q.usage_type,
            q.usage_value,
            q.usage_date,
            q.create_date
        FROM ai_quota_usage q
        <where>
            <if test="params.userId != null">
                AND q.user_id = #{params.userId}
            </if>
            <if test="params.deviceMac != null and params.deviceMac != ''">
                AND q.device_mac = #{params.deviceMac}
            </if>
            <if test="params.agentId != null and params.agentId != ''">
                AND q.agent_id = #{params.agentId}
            </if>
            <if test="params.usageType != null and params.usageType != ''">
                AND q.usage_type = #{params.usageType}
            </if>
            <if test="params.startDate != null and params.startDate != ''">
                AND q.usage_date >= #{params.startDate}
            </if>
            <if test="params.endDate != null and params.endDate != ''">
                AND q.usage_date &lt;= #{params.endDate}
            </if>
        </where>
        ORDER BY q.create_date DESC
    </select>

</mapper>
