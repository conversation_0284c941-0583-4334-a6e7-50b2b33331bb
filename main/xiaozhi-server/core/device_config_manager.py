"""
设备配置管理器
完善智能体管理与MAC地址的集成，提供设备专属配置管理
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class DeviceConfigNotFoundError(Exception):
    """设备配置未找到异常"""
    def __init__(self, mac_address: str):
        self.mac_address = mac_address
        super().__init__(f"设备配置未找到: {mac_address}")

class DeviceConfigManager:
    """设备配置管理器"""

    def __init__(self, config: Dict[str, Any], config_manager=None):
        self.config = config
        self.config_manager = config_manager

        # 设备配置缓存
        self.device_configs = {}
        self.config_timestamps = {}
        self.cache_ttl = 300  # 5分钟缓存

        # 默认配置
        self.default_config = {
            "delete_audio": True,
            "device_max_output_size": 10000,
            "chat_history_conf": 1,
            "selected_module": {
                "STT": "faster_whisper",
                "LLM": "openai",
                "TTS": "edge_tts",
                "Memory": "simple_memory",
                "Intent": "simple_intent"
            },
            "prompt": "[角色设定]\n你是一个叫nous ai的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话",
            "summaryMemory": None,
            "agent_id": None,
            "device_name": "未知设备"
        }

        logger.bind(tag=TAG).info("设备配置管理器初始化完成")

    async def get_device_config(self, mac_address: str, force_refresh: bool = False) -> Dict[str, Any]:
        """获取设备配置"""
        # 检查缓存
        if not force_refresh and self._is_config_cached(mac_address):
            logger.bind(tag=TAG).debug(f"使用缓存的设备配置: {mac_address}")
            return self.device_configs[mac_address]

        try:
            # 尝试从API获取配置
            if self.config_manager:
                device_config = await self.config_manager.get_device_config(mac_address, force_refresh)

                # 验证和补完配置
                validated_config = self._validate_and_complete_config(device_config, mac_address)

                # 更新缓存
                self._cache_device_config(mac_address, validated_config)

                logger.bind(tag=TAG).info(f"设备配置获取成功: {mac_address}")
                return validated_config

            # 如果没有配置管理器，使用默认配置
            default_config = self._get_default_config_for_device(mac_address)
            self._cache_device_config(mac_address, default_config)

            logger.bind(tag=TAG).warning(f"使用默认设备配置: {mac_address}")
            return default_config

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取设备配置失败: {mac_address}, {str(e)}")

            # 尝试使用过期缓存
            if mac_address in self.device_configs:
                logger.bind(tag=TAG).warning(f"使用过期的设备配置: {mac_address}")
                return self.device_configs[mac_address]

            # 最后回退到默认配置
            default_config = self._get_default_config_for_device(mac_address)
            logger.bind(tag=TAG).warning(f"回退到默认设备配置: {mac_address}")
            return default_config

    async def update_device_config(self, mac_address: str, config_updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新设备配置"""
        try:
            # 获取当前配置
            current_config = await self.get_device_config(mac_address)

            # 合并更新
            updated_config = self._merge_config_updates(current_config, config_updates)

            # 验证配置
            validated_config = self._validate_and_complete_config(updated_config, mac_address)

            # 更新缓存
            self._cache_device_config(mac_address, validated_config)

            logger.bind(tag=TAG).info(f"设备配置更新成功: {mac_address}")

            return {
                "status": "success",
                "mac_address": mac_address,
                "updated_config": validated_config,
                "message": "设备配置更新成功"
            }

        except Exception as e:
            logger.bind(tag=TAG).error(f"设备配置更新失败: {mac_address}, {str(e)}")
            return {
                "status": "error",
                "mac_address": mac_address,
                "error": str(e),
                "message": "设备配置更新失败"
            }

    async def get_agent_models(self, mac_address: str, client_name: str = "default", headers: Dict[str, str] = None) -> Dict[str, Any]:
        """获取智能体模型配置"""
        try:
            # 获取设备配置
            device_config = await self.get_device_config(mac_address)

            # 提取模型配置
            agent_models = {
                "STT": self._get_model_config("STT", device_config),
                "LLM": self._get_model_config("LLM", device_config),
                "TTS": self._get_model_config("TTS", device_config),
                "Memory": self._get_model_config("Memory", device_config),
                "Intent": self._get_model_config("Intent", device_config)
            }

            # 添加设备特定信息
            agent_models.update({
                "device_id": mac_address,
                "client_name": client_name,
                "prompt": device_config.get("prompt", self.default_config["prompt"]),
                "delete_audio": device_config.get("delete_audio", True),
                "device_max_output_size": device_config.get("device_max_output_size", 10000),
                "chat_history_conf": device_config.get("chat_history_conf", 1),
                "summaryMemory": device_config.get("summaryMemory")
            })

            logger.bind(tag=TAG).debug(f"智能体模型配置获取成功: {mac_address}")
            return agent_models

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取智能体模型配置失败: {mac_address}, {str(e)}")

            # 返回默认配置
            return self._get_default_agent_models(mac_address, client_name)

    async def validate_device_access(self, mac_address: str) -> Dict[str, Any]:
        """验证设备访问权限"""
        try:
            # 获取设备配置来验证设备是否存在
            device_config = await self.get_device_config(mac_address)

            # 检查设备状态
            device_status = {
                "is_registered": True,
                "is_active": True,
                "has_config": True,
                "agent_id": device_config.get("agent_id"),
                "device_name": device_config.get("device_name", "未知设备"),
                "last_access": datetime.now().isoformat()
            }

            # 检查配额限制（如果有配额管理器）
            try:
                from core.quota_manager import get_quota_manager
                quota_manager = get_quota_manager()
                if quota_manager:
                    quota_allowed, quota_info = await quota_manager.check_quota(mac_address)
                    device_status["quota_status"] = quota_info
                    device_status["quota_allowed"] = quota_allowed
            except Exception as e:
                logger.bind(tag=TAG).warning(f"检查配额状态失败: {str(e)}")
                device_status["quota_status"] = {"status": "unknown"}
                device_status["quota_allowed"] = True

            return {
                "status": "valid",
                "mac_address": mac_address,
                "device_status": device_status
            }

        except DeviceConfigNotFoundError:
            return {
                "status": "unregistered",
                "mac_address": mac_address,
                "message": "设备未注册"
            }
        except Exception as e:
            logger.bind(tag=TAG).error(f"验证设备访问权限失败: {mac_address}, {str(e)}")
            return {
                "status": "error",
                "mac_address": mac_address,
                "error": str(e)
            }

    def _validate_and_complete_config(self, config: Dict[str, Any], mac_address: str) -> Dict[str, Any]:
        """验证和补完设备配置"""
        # 从默认配置开始
        validated_config = self.default_config.copy()

        # 合并传入的配置
        if config:
            validated_config.update(config)

        # 确保必要字段存在
        if "device_id" not in validated_config:
            validated_config["device_id"] = mac_address

        # 验证模块配置
        if "selected_module" not in validated_config or not validated_config["selected_module"]:
            validated_config["selected_module"] = self.default_config["selected_module"].copy()
        else:
            # 补完缺失的模块
            for module, default_provider in self.default_config["selected_module"].items():
                if module not in validated_config["selected_module"]:
                    validated_config["selected_module"][module] = default_provider

        # 验证数值字段
        validated_config["device_max_output_size"] = max(
            1000,
            min(50000, validated_config.get("device_max_output_size", 10000))
        )

        validated_config["chat_history_conf"] = max(
            0,
            min(10, validated_config.get("chat_history_conf", 1))
        )

        return validated_config

    def _get_model_config(self, module_type: str, device_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取模块配置"""
        selected_modules = device_config.get("selected_module", {})
        provider = selected_modules.get(module_type, self.default_config["selected_module"][module_type])

        # 基础模型配置
        model_config = {
            "provider": provider,
            "enabled": True
        }

        # 添加模块特定配置
        if module_type == "LLM":
            model_config.update({
                "max_tokens": min(device_config.get("device_max_output_size", 10000) // 4, 4000),
                "temperature": 0.7,
                "prompt": device_config.get("prompt", self.default_config["prompt"])
            })
        elif module_type == "STT":
            model_config.update({
                "language": "zh-CN",
                "sample_rate": 16000
            })
        elif module_type == "TTS":
            model_config.update({
                "voice": "zh-CN-XiaoxiaoNeural",
                "rate": "medium"
            })
        elif module_type == "Memory":
            model_config.update({
                "history_length": device_config.get("chat_history_conf", 1),
                "summary_memory": device_config.get("summaryMemory")
            })

        return model_config

    def _get_default_config_for_device(self, mac_address: str) -> Dict[str, Any]:
        """获取设备的默认配置"""
        config = self.default_config.copy()
        config["device_id"] = mac_address
        config["device_name"] = f"设备_{mac_address[-6:]}"  # 使用MAC地址后6位作为设备名
        return config

    def _get_default_agent_models(self, mac_address: str, client_name: str) -> Dict[str, Any]:
        """获取默认智能体模型配置"""
        return {
            "STT": {"provider": "faster_whisper", "enabled": True},
            "LLM": {"provider": "openai", "enabled": True, "max_tokens": 2000},
            "TTS": {"provider": "edge_tts", "enabled": True},
            "Memory": {"provider": "simple_memory", "enabled": True},
            "Intent": {"provider": "simple_intent", "enabled": True},
            "device_id": mac_address,
            "client_name": client_name,
            "prompt": self.default_config["prompt"],
            "delete_audio": True,
            "device_max_output_size": 10000,
            "chat_history_conf": 1,
            "summaryMemory": None
        }

    def _merge_config_updates(self, current_config: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置更新"""
        merged_config = current_config.copy()

        for key, value in updates.items():
            if key == "selected_module" and isinstance(value, dict):
                # 深度合并模块配置
                merged_config["selected_module"] = merged_config.get("selected_module", {}).copy()
                merged_config["selected_module"].update(value)
            else:
                merged_config[key] = value

        return merged_config

    def _is_config_cached(self, mac_address: str) -> bool:
        """检查设备配置是否在缓存中且有效"""
        if mac_address not in self.device_configs:
            return False

        if mac_address not in self.config_timestamps:
            return False

        elapsed = time.time() - self.config_timestamps[mac_address]
        return elapsed < self.cache_ttl

    def _cache_device_config(self, mac_address: str, config: Dict[str, Any]):
        """缓存设备配置"""
        self.device_configs[mac_address] = config
        self.config_timestamps[mac_address] = time.time()

        logger.bind(tag=TAG).debug(f"设备配置已缓存: {mac_address}")

    def clear_device_cache(self, mac_address: str = None):
        """清除设备配置缓存"""
        if mac_address:
            self.device_configs.pop(mac_address, None)
            self.config_timestamps.pop(mac_address, None)
            logger.bind(tag=TAG).info(f"已清除设备配置缓存: {mac_address}")
        else:
            self.device_configs.clear()
            self.config_timestamps.clear()
            logger.bind(tag=TAG).info("已清除所有设备配置缓存")

    def get_cached_devices(self) -> List[str]:
        """获取已缓存的设备列表"""
        return list(self.device_configs.keys())

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()

        stats = {
            "total_cached_devices": len(self.device_configs),
            "cached_devices": list(self.device_configs.keys()),
            "cache_ages": {},
            "expired_configs": 0
        }

        for mac_address, timestamp in self.config_timestamps.items():
            age = current_time - timestamp
            stats["cache_ages"][mac_address] = age

            if age > self.cache_ttl:
                stats["expired_configs"] += 1

        return stats

# 全局设备配置管理器实例
_device_config_manager_instance: Optional[DeviceConfigManager] = None

def get_device_config_manager(config: Dict[str, Any] = None, config_manager=None) -> Optional[DeviceConfigManager]:
    """获取设备配置管理器实例"""
    global _device_config_manager_instance
    if _device_config_manager_instance is None and config is not None:
        _device_config_manager_instance = DeviceConfigManager(config, config_manager)
    return _device_config_manager_instance