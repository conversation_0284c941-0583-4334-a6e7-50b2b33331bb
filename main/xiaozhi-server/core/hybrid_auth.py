"""
二选一认证中间件：MAC地址认证 或 Token认证
- 启用MAC认证时：只使用MAC地址认证，失败时提示去官网激活
- 未启用MAC认证时：使用Token认证
- 配置manager-api后，所有MAC认证参数从后端API获取
- 确保至少有一种认证方式启用（二选一，不会都禁用）
"""

import asyncio
import re
from typing import Dict, Any
from loguru import logger

from core.auth import AuthMiddleware, AuthenticationError


class UnregisteredMacError(AuthenticationError):
    """未注册的MAC地址异常"""
    def __init__(self, mac_address):
        self.mac_address = mac_address
        super().__init__(f"未注册的MAC地址: {mac_address}，请前往官网注册您的设备")


def _is_valid_mac_format(mac_address: str) -> bool:
    """检查MAC地址格式是否有效"""
    if not mac_address:
        return False
    # 支持 XX:XX:XX:XX:XX:XX 和 XX-XX-XX-XX-XX-XX 格式
    mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    return bool(re.match(mac_pattern, mac_address))


class AlternativeAuthMiddleware:
    """二选一认证中间件：MAC地址认证 或 Token认证"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 确保至少有一种认证方式启用
        self._ensure_auth_enabled()

        # 初始化Token认证中间件
        self.token_auth = AuthMiddleware(config)

        # API配置
        self.api_config = config.get("manager-api", {})
        self.has_manager_api = bool(self.api_config.get("url") and self.api_config.get("secret"))

        logger.info(f"二选一认证中间件初始化完成: Manager API{'已配置' if self.has_manager_api else '未配置'}")

    def _ensure_auth_enabled(self):
        """确保二选一认证原则：MAC认证关闭时Token认证自动开启，MAC认证开启时Token认证自动关闭"""
        mac_auth_config = self.config.get("mac_auth", {})
        mac_auth_enabled = mac_auth_config.get("enabled", False)

        # 确保server.auth配置存在
        if "server" not in self.config:
            self.config["server"] = {}
        if "auth" not in self.config["server"]:
            self.config["server"]["auth"] = {}

        # 二选一逻辑：MAC认证关闭时Token认证自动开启，MAC认证开启时Token认证自动关闭
        if mac_auth_enabled:
            # MAC认证开启 → Token认证关闭
            self.config["server"]["auth"]["enabled"] = False
            logger.info("MAC认证已启用，Token认证自动关闭")
        else:
            # MAC认证关闭 → Token认证开启
            self.config["server"]["auth"]["enabled"] = True
            logger.info("MAC认证已关闭，Token认证自动开启")

    def _get_mac_auth_enabled(self) -> bool:
        """获取MAC认证是否启用（从配置中读取）"""
        # 优先从配置中的mac_auth.enabled读取
        mac_auth_config = self.config.get("mac_auth", {})
        enabled = mac_auth_config.get("enabled", False)

        # 打印详细的配置信息
        logger.info(f"MAC认证配置检查:")
        logger.info(f"  - mac_auth配置: {mac_auth_config}")
        logger.info(f"  - mac_auth.enabled: {enabled}")

        return enabled

    def _get_auto_register_enabled(self) -> bool:
        """获取自动注册是否启用（从配置中读取）"""
        # 统一从mac_auth.auto_register读取
        mac_auth_config = self.config.get("mac_auth", {})
        auto_register = mac_auth_config.get("auto_register", False)

        # 打印详细的调试信息
        logger.info(f"MAC自动注册配置检查:")
        logger.info(f"  - self.config类型: {type(self.config)}")
        logger.info(f"  - self.config.get('mac_auth')结果: {self.config.get('mac_auth')}")
        logger.info(f"  - mac_auth_config类型: {type(mac_auth_config)}")
        logger.info(f"  - mac_auth_config内容: {mac_auth_config}")
        logger.info(f"  - mac_auth_config.get('auto_register')结果: {mac_auth_config.get('auto_register')}")
        logger.info(f"  - auto_register最终值: {auto_register}")
        logger.info(f"  - auto_register类型: {type(auto_register)}")

        return auto_register

    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """
        认证流程：
        1. 如果启用MAC认证，优先使用MAC认证
        2. MAC认证失败时，抛出UnregisteredMacError异常
        3. 如果未启用MAC认证，直接使用Token认证
        """
        device_mac = headers.get("device-id", "")

        # 动态获取最新的MAC认证配置
        mac_auth_enabled = self._get_mac_auth_enabled()
        auto_register_enabled = self._get_auto_register_enabled()

        # 1. 尝试MAC地址预认证
        if mac_auth_enabled and device_mac:
            try:
                # 检查MAC地址格式
                if not _is_valid_mac_format(device_mac):
                    logger.warning(f"MAC地址格式无效: {device_mac}，抛出UnregisteredMacError")
                    raise UnregisteredMacError(device_mac)
                else:
                    # 检查设备是否已注册
                    if await self._check_mac_device_registered(device_mac):
                        logger.info(f"MAC地址预认证通过: {device_mac}")
                        headers["auth_success"] = "true"
                        headers["auth_method"] = "mac_device"
                        headers["device_bound"] = "true"
                        return True
                    else:
                        logger.info(f"MAC地址设备未注册: {device_mac}")

                        # 2. 尝试自动注册
                        if auto_register_enabled:
                            logger.info(f"开始尝试自动注册MAC地址: {device_mac}")
                            if await self._auto_register_mac_address(device_mac):
                                logger.info(f"MAC地址自动注册成功: {device_mac}")
                                headers["auth_success"] = "true"
                                headers["auth_method"] = "auto_register"
                                headers["device_bound"] = "true"
                                return True
                            else:
                                logger.warning(f"MAC地址自动注册失败: {device_mac}，抛出UnregisteredMacError")
                                raise UnregisteredMacError(device_mac)
                        else:
                            logger.info(f"自动注册未启用，抛出UnregisteredMacError")
                            raise UnregisteredMacError(device_mac)
            except UnregisteredMacError:
                # 重新抛出UnregisteredMacError异常
                raise
            except Exception as e:
                logger.warning(f"MAC地址认证失败: {str(e)}，抛出UnregisteredMacError")
                raise UnregisteredMacError(device_mac)

        # 3. 使用Token认证（MAC认证未启用时的备选方案）
        logger.info("MAC认证未启用，使用Token认证")
        return await self.token_auth.authenticate(headers)

    async def _check_mac_device_registered(self, mac_address: str) -> bool:
        """实时检查MAC地址对应的设备是否已注册"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法检查MAC地址设备注册状态")
            return False

        import aiohttp

        url = f"{self.api_config['url']}/device/auth/check/{mac_address}"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            is_registered = data.get("data", False)
                            logger.info(f"MAC地址 {mac_address} 设备注册检查结果: {is_registered}")
                            return is_registered
                        else:
                            logger.warning(f"MAC地址设备注册检查API返回错误: {data}")
                            return False
                    else:
                        logger.error(f"MAC地址设备注册检查API请求失败: HTTP {response.status}")
                        return False
        except asyncio.TimeoutError:
            logger.error(f"MAC地址设备注册检查超时: {mac_address}")
            return False
        except Exception as e:
            logger.error(f"MAC地址设备注册检查失败: {str(e)}")
            return False

    async def _auto_register_mac_address(self, mac_address: str) -> bool:
        """自动注册MAC地址对应的设备"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法进行MAC地址自动注册")
            return False

        import aiohttp

        url = f"{self.api_config['url']}/device/mac/auto-register"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }

        register_data = {
            "macAddress": mac_address,
            "remark": "自动注册设备"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=register_data, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            logger.info(f"MAC地址 {mac_address} 自动注册成功")
                            return True
                        else:
                            logger.error(f"MAC地址自动注册API返回错误: {data.get('msg', '未知错误')}")
                            return False
                    else:
                        logger.error(f"MAC地址自动注册API请求失败: HTTP {response.status}")
                        return False
        except asyncio.TimeoutError:
            logger.error(f"MAC地址自动注册超时: {mac_address}")
            return False
        except Exception as e:
            logger.error(f"MAC地址自动注册失败: {str(e)}")
            return False
