"""
配额管理系统
负责管理设备和账号的配额使用情况

用量限制规则：
1. 账号额度：每个账号拥有固定的每日总额度
2. 自定义智能体额度：每个自定义智能体可以使用全部账号额度
3. 累计使用原则：所有自定义智能体的用量累计计入同一账号额度
4. 官方默认智能体：每个设备统一限额
"""
import datetime
import asyncio
import aiohttp
import time
import functools
from typing import Dict, Optional, List
from config.logger import setup_logging
from config.config_loader import get_config_from_api

TAG = __name__
logger = setup_logging()

# 全局字典，用于存储每个设备的每日输出字数（仅官方默认智能体）
_device_daily_output: Dict[str, int] = {}
# 全局字典，用于存储每个账号的每日输出字数（自定义智能体累计）
_account_daily_output: Dict[int, int] = {}
# 全局字典，用于存储设备与智能体的关联关系
_device_agent_mapping: Dict[str, str] = {}
# 全局字典，用于存储智能体与账号的关联关系
_agent_account_mapping: Dict[str, int] = {}
# 全局字典，用于存储智能体是否为官方默认智能体
_default_agents: Dict[str, bool] = {}
# 记录最后一次检查的日期
_last_check_date: datetime.date = None
# 锁，用于保护配额数据的并发访问
_quota_lock = asyncio.Lock()


class QuotaManager:
    """配额管理器"""

    def __init__(self, config):
        """初始化配额管理器"""
        self.config = config

        # 获取数据库API配置，复用与auth.py相同的配置获取方式
        self.manager_api_config = config.get("manager-api", {})
        self.api_base_url = self.manager_api_config.get("url")
        self.api_key = self.manager_api_config.get("secret")

        # 配额设置
        self.device_default_quota = int(config.get("quota", {}).get("device_default_quota", 10000))  # 官方默认智能体的设备限额
        self.account_default_quota = int(config.get("quota", {}).get("account_default_quota", 100000))  # 账号每日总额度

        # 是否启用配额限制
        self.enable_quota_limit = config.get("quota", {}).get("enabled", True)

        # 缓存设置
        self.cache_ttl = 300  # 缓存有效期5分钟
        self.last_refresh_time = 0

        # LRU缓存装饰器（避免全量拉取，按需查询）
        self._device_agent_lru = functools.lru_cache(maxsize=1024)(self._fetch_device_agent_from_api)
        self._agent_account_lru = functools.lru_cache(maxsize=512)(self._fetch_agent_account_from_api)

        # 初始化（不再全量拉取映射关系）
        self.quota_initialized = True
        logger.bind(tag=TAG).info("配额管理器初始化完成，启用按需查询模式")

    def _fetch_device_agent_from_api(self, mac_address: str) -> str:
        """同步调用API获取单个MAC的agentId（用于LRU缓存装饰）"""
        import requests
        if not self.api_base_url or not self.api_key:
            return None
        url = f"{self.api_base_url}/device/mapping/{mac_address}"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        try:
            resp = requests.get(url, headers=headers, timeout=3)
            if resp.status_code == 200:
                data = resp.json()
                if data.get("data") and data["data"].get("agentId"):
                    return data["data"]["agentId"]
        except Exception as e:
            logger.bind(tag=TAG).error(f"API获取单条MAC映射失败: {mac_address}, {str(e)}")
        return None

    def _fetch_agent_account_from_api(self, agent_id: str) -> tuple:
        """同步调用API获取智能体的账号ID和默认标志（用于LRU缓存装饰）"""
        import requests
        if not self.api_base_url or not self.api_key:
            return None, False
        url = f"{self.api_base_url}/agent/mapping"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        try:
            resp = requests.get(url, headers=headers, timeout=3)
            if resp.status_code == 200:
                data = resp.json()
                if data.get("data") and isinstance(data["data"], list):
                    for item in data["data"]:
                        if item.get("id") == agent_id:
                            return item.get("userId"), item.get("isDefault", False)
        except Exception as e:
            logger.bind(tag=TAG).error(f"API获取智能体映射失败: {agent_id}, {str(e)}")
        return None, False

    async def _init_quota_data(self):
        """初始化配额数据"""
        try:
            await self._refresh_quota_data()
            # 设置定期刷新任务
            asyncio.create_task(self._periodic_refresh())
        except Exception as e:
            logger.bind(tag=TAG).error(f"初始化配额数据失败: {str(e)}")

    async def _periodic_refresh(self):
        """定期刷新配额数据"""
        while True:
            await asyncio.sleep(self.cache_ttl)
            try:
                await self._refresh_quota_data()
            except Exception as e:
                logger.bind(tag=TAG).error(f"刷新配额数据失败: {str(e)}")

    async def _refresh_quota_data(self):
        """从API刷新配额数据"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                # 获取设备-智能体映射关系
                async with session.get(f"{self.api_base_url}/device/mapping", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data and isinstance(data["data"], list):
                            async with _quota_lock:
                                global _device_agent_mapping
                                _device_agent_mapping = {item["macAddress"]: item["agentId"] for item in data["data"] if item.get("macAddress") and item.get("agentId")}
                                logger.bind(tag=TAG).info(f"已刷新设备-智能体映射关系，共 {len(_device_agent_mapping)} 个映射")

                # 获取智能体-账号映射关系
                async with session.get(f"{self.api_base_url}/agent/mapping", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data and isinstance(data["data"], list):
                            async with _quota_lock:
                                global _agent_account_mapping, _default_agents
                                _agent_account_mapping = {item["id"]: item["userId"] for item in data["data"] if item.get("id") and item.get("userId")}
                                _default_agents = {item["id"]: item.get("isDefault", False) for item in data["data"] if item.get("id")}
                                logger.bind(tag=TAG).info(f"已刷新智能体-账号映射关系，共 {len(_agent_account_mapping)} 个映射")

                # 获取配额设置
                async with session.get(f"{self.api_base_url}/quota/settings", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "data" in data:
                            self.device_default_quota = int(data["data"].get("deviceDefaultQuota", self.device_default_quota))
                            self.account_default_quota = int(data["data"].get("accountDefaultQuota", self.account_default_quota))
                            self.enable_quota_limit = bool(data["data"].get("enableQuotaLimit", self.enable_quota_limit))
                            logger.bind(tag=TAG).info(
                                f"已刷新配额设置: 设备默认配额={self.device_default_quota}, "
                                f"账号默认配额={self.account_default_quota}, "
                                f"启用配额限制={self.enable_quota_limit}"
                            )

                self.last_refresh_time = time.time()
            except Exception as e:
                logger.bind(tag=TAG).error(f"请求配额数据异常: {str(e)}")

    async def reset_daily_quota(self):
        """重置每日配额"""
        async with _quota_lock:
            global _device_daily_output, _account_daily_output, _last_check_date
            _device_daily_output.clear()
            _account_daily_output.clear()
            _last_check_date = datetime.datetime.now().date()
            logger.bind(tag=TAG).info("已重置每日配额")

    async def get_device_quota_usage(self, mac_address: str) -> int:
        """获取设备当日的配额使用量（仅适用于官方默认智能体）"""
        current_date = datetime.datetime.now().date()
        global _last_check_date

        # 如果是第一次调用或者日期发生变化，清空计数器
        if _last_check_date is None or _last_check_date != current_date:
            await self.reset_daily_quota()

        async with _quota_lock:
            return _device_daily_output.get(mac_address, 0)

    async def get_account_quota_usage(self, user_id: int) -> int:
        """获取账号当日的配额使用量（自定义智能体累计）"""
        current_date = datetime.datetime.now().date()
        global _last_check_date

        # 如果是第一次调用或者日期发生变化，清空计数器
        if _last_check_date is None or _last_check_date != current_date:
            await self.reset_daily_quota()

        async with _quota_lock:
            return _account_daily_output.get(user_id, 0)

    async def get_device_agent_id(self, mac_address: str) -> Optional[str]:
        """获取设备关联的智能体ID（使用LRU缓存，按需查询）"""
        try:
            return self._device_agent_lru(mac_address)
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取设备agentId失败: {mac_address}, {str(e)}")
            return None

    async def get_agent_user_id(self, agent_id: str) -> Optional[int]:
        """获取智能体关联的用户ID（使用LRU缓存，按需查询）"""
        try:
            user_id, _ = self._agent_account_lru(agent_id)
            return user_id
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取智能体userId失败: {agent_id}, {str(e)}")
            return None

    async def is_default_agent(self, agent_id: str) -> bool:
        """检查智能体是否为官方默认智能体（使用LRU缓存，按需查询）"""
        if not agent_id:
            return True  # 默认处理为官方智能体
        try:
            _, is_default = self._agent_account_lru(agent_id)
            return is_default
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取智能体默认标志失败: {agent_id}, {str(e)}")
            return True  # 默认处理为官方智能体

    async def add_quota_usage(self, mac_address: str, char_count: int) -> bool:
        """
        增加配额使用量
        根据智能体类型自动选择限制策略：
        - 官方默认智能体：使用设备级别限制
        - 自定义智能体：使用账号级别限制（累计计算）
        
        返回是否成功（如果超出配额限制则返回False）
        """
        # 如果未启用配额限制，直接返回成功
        if not self.enable_quota_limit:
            return True

        # 获取设备关联的智能体ID
        agent_id = await self.get_device_agent_id(mac_address)
        if not agent_id:
            logger.bind(tag=TAG).warning(f"设备 {mac_address} 未关联智能体，拒绝请求")
            return False

        # 检查是否为官方默认智能体
        is_default = await self.is_default_agent(agent_id)
        
        if is_default:
            # 官方默认智能体：使用设备级别限制
            device_usage = await self.get_device_quota_usage(mac_address)
            if device_usage + char_count > self.device_default_quota and self.device_default_quota > 0:
                logger.bind(tag=TAG).warning(f"设备 {mac_address} 超出配额限制: {device_usage + char_count}/{self.device_default_quota}")
                return False

            # 更新设备配额使用量
            async with _quota_lock:
                global _device_daily_output
                _device_daily_output[mac_address] = device_usage + char_count
                logger.bind(tag=TAG).debug(f"设备 {mac_address} 配额使用量更新: {device_usage + char_count}/{self.device_default_quota}")
        else:
            # 自定义智能体：使用账号级别限制
            user_id = await self.get_agent_user_id(agent_id)
            if not user_id:
                logger.bind(tag=TAG).warning(f"智能体 {agent_id} 未关联用户，拒绝请求")
                return False

            account_usage = await self.get_account_quota_usage(user_id)
            if account_usage + char_count > self.account_default_quota and self.account_default_quota > 0:
                logger.bind(tag=TAG).warning(f"账号 {user_id} 超出配额限制: {account_usage + char_count}/{self.account_default_quota}")
                return False

            # 更新账号配额使用量
            async with _quota_lock:
                global _account_daily_output
                _account_daily_output[user_id] = account_usage + char_count
                logger.bind(tag=TAG).debug(f"账号 {user_id} 配额使用量更新: {account_usage + char_count}/{self.account_default_quota}")

        return True

    async def add_device_quota_usage(self, mac_address: str, char_count: int) -> bool:
        """
        增加设备的配额使用量
        兼容性方法，内部调用新的add_quota_usage方法
        """
        return await self.add_quota_usage(mac_address, char_count)

    async def check_quota_limit(self, mac_address: str, char_count: int) -> bool:
        """
        检查是否超出配额限制（不实际增加使用量）
        根据智能体类型自动选择限制策略
        """
        # 如果未启用配额限制，直接返回成功
        if not self.enable_quota_limit:
            return True

        # 获取设备关联的智能体ID
        agent_id = await self.get_device_agent_id(mac_address)
        if not agent_id:
            return False

        # 检查是否为官方默认智能体
        is_default = await self.is_default_agent(agent_id)
        
        if is_default:
            # 官方默认智能体：检查设备级别限制
            device_usage = await self.get_device_quota_usage(mac_address)
            return device_usage + char_count <= self.device_default_quota or self.device_default_quota <= 0
        else:
            # 自定义智能体：检查账号级别限制
            user_id = await self.get_agent_user_id(agent_id)
            if not user_id:
                return False
            
            account_usage = await self.get_account_quota_usage(user_id)
            return account_usage + char_count <= self.account_default_quota or self.account_default_quota <= 0


def get_quota_manager(config=None):
    """获取配额管理器实例"""
    if not hasattr(get_quota_manager, '_instance'):
        if config is None:
            # 如果没有提供配置，从config_loader获取
            config = get_config_from_api(config or {})
        get_quota_manager._instance = QuotaManager(config)
    return get_quota_manager._instance
