"""
Token使用量统计模块
与Java端QuotaService保持一致的token统计逻辑

配额类型说明：
- 设备级配额：默认智能体（ai_default_agent表）按MAC地址统计
- 账号级配额：用户智能体（ai_agent表）按用户ID统计
"""
import datetime
from typing import Dict, Tuple, Optional
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

# 设备级token使用量缓存 {(device_id, date): tokens}
_device_daily_tokens: Dict[Tuple[str, datetime.date], int] = {}

# 账号级token使用量缓存 {(user_id, date): tokens}  
_account_daily_tokens: Dict[Tuple[str, datetime.date], int] = {}

# 记录最后检查日期
_last_check_date: datetime.date = None


def reset_token_counters():
    """重置所有token计数器（每天0点调用）"""
    global _device_daily_tokens, _account_daily_tokens
    _device_daily_tokens.clear()
    _account_daily_tokens.clear()
    logger.bind(tag=TAG).info("Token计数器已重置")


def get_device_tokens(device_id: str) -> int:
    """获取设备当日的token使用量
    
    Args:
        device_id: 设备ID（MAC地址）
        
    Returns:
        int: 当日token使用量
    """
    if not device_id:
        return 0
    
    current_date = datetime.datetime.now().date()
    tokens = _device_daily_tokens.get((device_id, current_date), 0)
    logger.bind(tag=TAG).debug(f"获取设备token使用量: {device_id} = {tokens}")
    return tokens


def get_account_tokens(user_id: str) -> int:
    """获取账号当日的token使用量
    
    Args:
        user_id: 用户ID
        
    Returns:
        int: 当日token使用量
    """
    if not user_id:
        return 0
    
    current_date = datetime.datetime.now().date()
    tokens = _account_daily_tokens.get((user_id, current_date), 0)
    logger.bind(tag=TAG).debug(f"获取账号token使用量: {user_id} = {tokens}")
    return tokens


def add_device_tokens(device_id: str, token_count: int):
    """增加设备的token使用量
    
    Args:
        device_id: 设备ID（MAC地址）
        token_count: 增加的token数量
    """
    if not device_id or token_count <= 0:
        return
    
    current_date = datetime.datetime.now().date()
    global _last_check_date
    
    # 日期变化时清空计数器
    if _last_check_date is None or _last_check_date != current_date:
        reset_token_counters()
        _last_check_date = current_date
    
    current_count = _device_daily_tokens.get((device_id, current_date), 0)
    new_count = current_count + token_count
    _device_daily_tokens[(device_id, current_date)] = new_count
    
    logger.bind(tag=TAG).debug(f"设备token使用量更新: {device_id} +{token_count} = {new_count}")


def add_account_tokens(user_id: str, token_count: int):
    """增加账号的token使用量
    
    Args:
        user_id: 用户ID
        token_count: 增加的token数量
    """
    if not user_id or token_count <= 0:
        return
    
    current_date = datetime.datetime.now().date()
    global _last_check_date
    
    # 日期变化时清空计数器
    if _last_check_date is None or _last_check_date != current_date:
        reset_token_counters()
        _last_check_date = current_date
    
    current_count = _account_daily_tokens.get((user_id, current_date), 0)
    new_count = current_count + token_count
    _account_daily_tokens[(user_id, current_date)] = new_count
    
    logger.bind(tag=TAG).debug(f"账号token使用量更新: {user_id} +{token_count} = {new_count}")


def check_device_token_limit(device_id: str, max_tokens: int) -> bool:
    """检查设备是否超过token限制
    
    Args:
        device_id: 设备ID（MAC地址）
        max_tokens: 最大token限制
        
    Returns:
        bool: True表示超过限制，False表示未超过
    """
    if not device_id or max_tokens <= 0:
        return False
    
    current_tokens = get_device_tokens(device_id)
    is_exceeded = current_tokens >= max_tokens
    
    if is_exceeded:
        logger.bind(tag=TAG).warning(f"设备token配额超限: {device_id}, 当前: {current_tokens}, 限制: {max_tokens}")
    
    return is_exceeded


def check_account_token_limit(user_id: str, max_tokens: int) -> bool:
    """检查账号是否超过token限制
    
    Args:
        user_id: 用户ID
        max_tokens: 最大token限制
        
    Returns:
        bool: True表示超过限制，False表示未超过
    """
    if not user_id or max_tokens <= 0:
        return False
    
    current_tokens = get_account_tokens(user_id)
    is_exceeded = current_tokens >= max_tokens
    
    if is_exceeded:
        logger.bind(tag=TAG).warning(f"账号token配额超限: {user_id}, 当前: {current_tokens}, 限制: {max_tokens}")
    
    return is_exceeded


def estimate_tokens_from_text(text: str, model_type: str = "general") -> int:
    """根据文本估算token数量
    
    Args:
        text: 输入文本
        model_type: 模型类型，用于选择估算比例
        
    Returns:
        int: 估算的token数量
    """
    if not text:
        return 0
    
    # 不同模型的估算比例（字符数 -> token数的转换比例）
    ratios = {
        "openai": 0.75,      # OpenAI模型：英文约4字符=1token，中文约2.5字符=1token
        "chinese": 0.4,      # 中文模型：约2.5字符=1token
        "general": 0.5,      # 通用估算：约2字符=1token
        "ollama": 0.6,       # Ollama模型
        "xinference": 0.5,   # Xinference模型
    }
    
    ratio = ratios.get(model_type.lower(), 0.5)
    estimated_tokens = int(len(text) * ratio)
    
    logger.bind(tag=TAG).debug(f"Token估算: 文本长度={len(text)}, 估算tokens={estimated_tokens}, 模型类型={model_type}")
    return estimated_tokens


def get_token_usage_stats(device_id: str = None, user_id: str = None) -> Dict[str, int]:
    """获取token使用量统计信息
    
    Args:
        device_id: 设备ID（可选）
        user_id: 用户ID（可选）
        
    Returns:
        Dict[str, int]: 统计信息
    """
    stats = {}
    
    if device_id:
        stats["device_tokens"] = get_device_tokens(device_id)
    
    if user_id:
        stats["account_tokens"] = get_account_tokens(user_id)
    
    # 总体统计
    current_date = datetime.datetime.now().date()
    total_device_tokens = sum(
        tokens for (_, date), tokens in _device_daily_tokens.items() 
        if date == current_date
    )
    total_account_tokens = sum(
        tokens for (_, date), tokens in _account_daily_tokens.items() 
        if date == current_date
    )
    
    stats.update({
        "total_device_tokens": total_device_tokens,
        "total_account_tokens": total_account_tokens,
        "total_tokens": total_device_tokens + total_account_tokens,
        "date": current_date.isoformat()
    })
    
    return stats


def clear_expired_cache():
    """清理过期的缓存数据（保留最近7天）"""
    current_date = datetime.datetime.now().date()
    cutoff_date = current_date - datetime.timedelta(days=7)
    
    # 清理设备token缓存
    expired_device_keys = [
        key for key in _device_daily_tokens.keys() 
        if key[1] < cutoff_date
    ]
    for key in expired_device_keys:
        del _device_daily_tokens[key]
    
    # 清理账号token缓存
    expired_account_keys = [
        key for key in _account_daily_tokens.keys() 
        if key[1] < cutoff_date
    ]
    for key in expired_account_keys:
        del _account_daily_tokens[key]
    
    if expired_device_keys or expired_account_keys:
        logger.bind(tag=TAG).info(f"清理过期token缓存: 设备{len(expired_device_keys)}条, 账号{len(expired_account_keys)}条")
