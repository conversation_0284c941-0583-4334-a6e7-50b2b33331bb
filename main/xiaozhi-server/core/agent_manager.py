"""
智能体管理器
负责管理设备与智能体的关联关系，以及智能体的配置获取
"""
import asyncio
import aiohttp
import json
import time
import functools
from typing import Dict, Any, Optional, List
from config.logger import setup_logging
from config.config_loader import get_config_from_api

TAG = __name__
logger = setup_logging()

class AgentManager:
    """智能体管理器"""

    def __init__(self, config):
        """初始化智能体管理器"""
        self.config = config

        # 获取数据库API配置，复用与auth.py相同的配置获取方式
        self.manager_api_config = config.get("manager-api", {})
        self.api_base_url = self.manager_api_config.get("url")
        self.api_key = self.manager_api_config.get("secret")

        # 缓存（保留设备-智能体关联缓存，因为可能还有其他地方使用）
        self.device_agent_cache = {}  # 设备-智能体关联缓存

        # 锁，用于保护缓存的并发访问
        self.cache_lock = asyncio.Lock()

        # LRU缓存装饰器（避免全量拉取，按需查询）
        self._device_agent_lru = functools.lru_cache(maxsize=1024)(self._fetch_device_agent_from_api)

        # 注意：不再需要初始化智能体数据，因为配置通过 /config/agent-models 接口获取

    # 注意：以下方法已废弃，因为配置通过 /config/agent-models 接口获取
    # 不再需要定期刷新，配置更新通过清除Redis缓存实现

    async def _refresh_default_agent(self):
        """刷新默认智能体配置（已废弃）"""
        # 注意：此方法已废弃，因为默认智能体配置通过 /config/agent-models 接口获取
        # Python端实际使用的是 connection._initialize_private_config() 方法
        logger.bind(tag=TAG).debug("_refresh_default_agent 方法已废弃，配置通过 /config/agent-models 接口获取")

    async def get_agent_config(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取智能体配置（已废弃）"""
        # 注意：此方法已废弃，因为智能体配置通过 /config/agent-models 接口获取
        # Python端实际使用的是 connection._initialize_private_config() 方法
        logger.bind(tag=TAG).debug("get_agent_config 方法已废弃，配置通过 /config/agent-models 接口获取")
        return None

    def _fetch_device_agent_from_api(self, mac_address: str) -> str:
        """同步调用API获取单个MAC的agentId（用于LRU缓存装饰）"""
        import requests
        if not self.api_base_url or not self.api_key:
            return None
        url = f"{self.api_base_url}/device/mapping/{mac_address}"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        try:
            resp = requests.get(url, headers=headers, timeout=3)
            if resp.status_code == 200:
                data = resp.json()
                if data.get("data") and data["data"].get("agentId"):
                    return data["data"]["agentId"]
        except Exception as e:
            logger.bind(tag=TAG).error(f"API获取单条MAC映射失败: {mac_address}, {str(e)}")
        return None

    async def get_device_agent_id(self, mac_address: str) -> Optional[str]:
        """获取设备关联的智能体ID（使用LRU缓存，按需查询）"""
        try:
            return self._device_agent_lru(mac_address)
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取设备agentId失败: {mac_address}, {str(e)}")
            return None

    async def get_device_config(self, mac_address: str) -> Dict[str, Any]:
        """
        获取设备的配置（已废弃）

        注意：此方法已废弃，因为设备配置通过 /config/agent-models 接口获取
        Python端实际使用的是 connection._initialize_private_config() 方法
        """
        logger.bind(tag=TAG).debug("get_device_config 方法已废弃，配置通过 /config/agent-models 接口获取")
        return {}

    async def update_device_agent(self, mac_address: str, agent_id: str) -> bool:
        """更新设备关联的智能体"""
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            try:
                data = {"macAddress": mac_address, "agentId": agent_id}
                async with session.post(
                    f"{self.api_base_url}/device/update-agent",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        async with self.cache_lock:
                            self.device_agent_cache[mac_address] = agent_id
                        logger.bind(tag=TAG).info(f"已更新设备 {mac_address} 关联的智能体为 {agent_id}")
                        return True
                    else:
                        logger.bind(tag=TAG).error(f"更新设备智能体关联失败: {response.status}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"更新设备智能体关联异常: {str(e)}")

        return False


# 创建全局单例
_agent_manager_instance = None

def get_agent_manager(config=None):
    """获取智能体管理器单例"""
    global _agent_manager_instance
    if _agent_manager_instance is None and config is not None:
        _agent_manager_instance = AgentManager(config)
    return _agent_manager_instance
