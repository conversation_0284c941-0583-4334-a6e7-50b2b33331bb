"""统一异常处理中间件"""
from typing import Callable, Awaitable, Dict, Any, Optional
from websockets import WebSocketServerProtocol
import json
from config.logger import setup_logging
from core.auth import AuthenticationError
from core.mac_db_auth import UnregisteredMacError

logger = setup_logging()

class APIError(Exception):
    """基础API异常"""
    def __init__(self, message: str, code: int = 400, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(message)

class RateLimitError(APIError):
    """限流异常"""
    def __init__(self, message: str = "请求过于频繁", retry_after: int = 60):
        super().__init__(message, code=429)
        self.details["retry_after"] = retry_after

class ValidationError(APIError):
    """参数验证异常"""
    def __init__(self, message: str = "参数验证失败", errors: Optional[Dict[str, Any]] = None):
        super().__init__(message, code=422)
        self.details["errors"] = errors or {}

async def exception_middleware(
    websocket: WebSocketServerProtocol,
    handler: Callable[[WebSocketServerProtocol], Awaitable[None]]
) -> None:
    """WebSocket异常处理中间件"""
    try:
        await handler(websocket)
    except UnregisteredMacError as e:
        await _send_error(websocket, str(e), 403, {"mac_address": e.mac_address})
    except AuthenticationError as e:
        await _send_error(websocket, str(e), 401)
    except RateLimitError as e:
        await _send_error(websocket, e.message, e.code, e.details)
    except ValidationError as e:
        await _send_error(websocket, e.message, e.code, e.details)
    except APIError as e:
        await _send_error(websocket, e.message, e.code, e.details)
    except Exception as e:
        logger.exception("未处理的异常")
        await _send_error(websocket, "服务器内部错误", 500)

async def _send_error(
    websocket: WebSocketServerProtocol,
    message: str,
    code: int,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """发送标准化错误响应"""
    error_response = {
        "type": "error",
        "code": code,
        "message": message,
        "details": details or {}
    }
    try:
        await websocket.send(json.dumps(error_response))
    except Exception as e:
        logger.error(f"发送错误响应失败: {e}")