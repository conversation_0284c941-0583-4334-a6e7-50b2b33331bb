"""
统计管理器
记录和监控MAC地址认证相关的统计信息，提供运营数据支持
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from collections import defaultdict, deque

from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class StatisticsManager:
    """统计管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("statistics", {}).get("enabled", True)
        
        # 认证统计
        self.auth_stats = {
            "total_attempts": 0,
            "success_count": 0,
            "failure_count": 0,
            "unregistered_count": 0,
            "last_reset": time.time()
        }
        
        # 设备统计
        self.device_stats = defaultdict(lambda: {
            "total_requests": 0,
            "successful_auths": 0,
            "failed_auths": 0,
            "last_access": None,
            "first_access": None,
            "is_registered": False,
            "violation_count": 0
        })
        
        # 时间序列统计（最近24小时，每小时一个数据点）
        self.hourly_stats = deque(maxlen=24)
        self._init_hourly_stats()
        
        # 错误统计
        self.error_stats = defaultdict(int)
        
        # 性能统计
        self.performance_stats = {
            "auth_avg_time": 0.0,
            "config_fetch_avg_time": 0.0,
            "api_response_times": deque(maxlen=100)
        }
        
        logger.bind(tag=TAG).info(f"统计管理器初始化完成, 启用状态: {self.enabled}")
    
    async def record_auth_attempt(self, mac_address: str, success: bool, auth_type: str = "mac", 
                                error: str = None, response_time: float = None) -> Dict[str, Any]:
        """记录认证尝试"""
        if not self.enabled:
            return {"status": "disabled"}
        
        current_time = datetime.now()
        
        # 更新全局统计
        self.auth_stats["total_attempts"] += 1
        if success:
            self.auth_stats["success_count"] += 1
        else:
            self.auth_stats["failure_count"] += 1
        
        # 更新设备统计
        device_stat = self.device_stats[mac_address]
        device_stat["total_requests"] += 1
        device_stat["last_access"] = current_time.isoformat()
        
        if device_stat["first_access"] is None:
            device_stat["first_access"] = current_time.isoformat()
        
        if success:
            device_stat["successful_auths"] += 1
            device_stat["is_registered"] = True
        else:
            device_stat["failed_auths"] += 1
            if error == "unregistered":
                self.auth_stats["unregistered_count"] += 1
            else:
                device_stat["violation_count"] += 1
        
        # 记录错误
        if error:
            self.error_stats[error] += 1
        
        # 记录响应时间
        if response_time:
            self.performance_stats["api_response_times"].append(response_time)
            if auth_type == "mac":
                self._update_avg_time("auth_avg_time", response_time)
        
        # 更新小时统计
        self._update_hourly_stats(success, auth_type)
        
        logger.bind(tag=TAG).debug(
            f"认证尝试记录: {mac_address}, 成功: {success}, 类型: {auth_type}, 错误: {error}"
        )
        
        return {
            "status": "recorded",
            "mac_address": mac_address,
            "success": success,
            "timestamp": current_time.isoformat()
        }
    
    async def record_unregistered_device(self, mac_address: str) -> Dict[str, Any]:
        """记录未注册设备尝试"""
        if not self.enabled:
            return {"status": "disabled"}
        
        # 记录认证失败
        await self.record_auth_attempt(mac_address, False, "mac", "unregistered")
        
        logger.bind(tag=TAG).warning(f"未注册设备尝试记录: {mac_address}")
        
        return {
            "status": "recorded",
            "mac_address": mac_address,
            "type": "unregistered_device"
        }
    
    async def record_config_fetch(self, mac_address: str, success: bool, response_time: float = None,
                                config_type: str = "device") -> Dict[str, Any]:
        """记录配置获取"""
        if not self.enabled:
            return {"status": "disabled"}
        
        # 记录响应时间
        if response_time:
            self.performance_stats["api_response_times"].append(response_time)
            if config_type == "mac_auth":
                self._update_avg_time("config_fetch_avg_time", response_time)
        
        # 更新设备统计
        if mac_address:
            device_stat = self.device_stats[mac_address]
            if not success:
                device_stat["violation_count"] += 1
        
        logger.bind(tag=TAG).debug(f"配置获取记录: {mac_address}, 成功: {success}, 类型: {config_type}")
        
        return {
            "status": "recorded",
            "config_type": config_type,
            "success": success
        }
    
    def get_auth_statistics(self) -> Dict[str, Any]:
        """获取认证统计信息"""
        success_rate = 0.0
        if self.auth_stats["total_attempts"] > 0:
            success_rate = self.auth_stats["success_count"] / self.auth_stats["total_attempts"] * 100
        
        return {
            "total_attempts": self.auth_stats["total_attempts"],
            "success_count": self.auth_stats["success_count"],
            "failure_count": self.auth_stats["failure_count"],
            "unregistered_count": self.auth_stats["unregistered_count"],
            "success_rate": round(success_rate, 2),
            "last_reset": datetime.fromtimestamp(self.auth_stats["last_reset"]).isoformat(),
            "uptime_hours": round((time.time() - self.auth_stats["last_reset"]) / 3600, 2)
        }
    
    def get_device_statistics(self, mac_address: str = None) -> Dict[str, Any]:
        """获取设备统计信息"""
        if mac_address:
            # 返回特定设备统计
            if mac_address not in self.device_stats:
                return {
                    "status": "no_data",
                    "mac_address": mac_address
                }
            
            stats = self.device_stats[mac_address]
            success_rate = 0.0
            if stats["total_requests"] > 0:
                success_rate = stats["successful_auths"] / stats["total_requests"] * 100
            
            return {
                "status": "success",
                "mac_address": mac_address,
                "total_requests": stats["total_requests"],
                "successful_auths": stats["successful_auths"],
                "failed_auths": stats["failed_auths"],
                "success_rate": round(success_rate, 2),
                "last_access": stats["last_access"],
                "first_access": stats["first_access"],
                "is_registered": stats["is_registered"],
                "violation_count": stats["violation_count"]
            }
        else:
            # 返回所有设备统计摘要
            total_devices = len(self.device_stats)
            registered_devices = sum(1 for stats in self.device_stats.values() if stats["is_registered"])
            active_devices = sum(1 for stats in self.device_stats.values() 
                               if stats["last_access"] and 
                               datetime.fromisoformat(stats["last_access"]) > datetime.now() - timedelta(hours=24))
            
            return {
                "status": "success",
                "total_devices": total_devices,
                "registered_devices": registered_devices,
                "unregistered_devices": total_devices - registered_devices,
                "active_devices_24h": active_devices,
                "top_devices": self._get_top_devices(5)
            }
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = sum(self.error_stats.values())
        
        error_breakdown = {}
        for error_type, count in self.error_stats.items():
            percentage = (count / total_errors * 100) if total_errors > 0 else 0
            error_breakdown[error_type] = {
                "count": count,
                "percentage": round(percentage, 2)
            }
        
        return {
            "total_errors": total_errors,
            "error_breakdown": error_breakdown,
            "most_common_error": max(self.error_stats.items(), key=lambda x: x[1])[0] if self.error_stats else None
        }
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        response_times = list(self.performance_stats["api_response_times"])
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = min_response_time = max_response_time = 0.0
        
        return {
            "auth_avg_time": self.performance_stats["auth_avg_time"],
            "config_fetch_avg_time": self.performance_stats["config_fetch_avg_time"],
            "api_response_stats": {
                "avg_time": round(avg_response_time, 3),
                "min_time": round(min_response_time, 3),
                "max_time": round(max_response_time, 3),
                "sample_count": len(response_times)
            }
        }
    
    def get_hourly_statistics(self) -> List[Dict[str, Any]]:
        """获取小时统计信息"""
        return list(self.hourly_stats)
    
    def get_comprehensive_report(self) -> Dict[str, Any]:
        """获取综合统计报告"""
        return {
            "auth_statistics": self.get_auth_statistics(),
            "device_statistics": self.get_device_statistics(),
            "error_statistics": self.get_error_statistics(),
            "performance_statistics": self.get_performance_statistics(),
            "hourly_trend": list(self.hourly_stats)[-12:],  # 最近12小时
            "system_info": {
                "statistics_enabled": self.enabled,
                "report_generated": datetime.now().isoformat()
            }
        }
    
    def reset_statistics(self, scope: str = "all") -> Dict[str, Any]:
        """重置统计信息"""
        reset_items = []
        
        if scope in ["all", "auth"]:
            self.auth_stats = {
                "total_attempts": 0,
                "success_count": 0,
                "failure_count": 0,
                "unregistered_count": 0,
                "last_reset": time.time()
            }
            reset_items.append("auth_stats")
        
        if scope in ["all", "devices"]:
            self.device_stats.clear()
            reset_items.append("device_stats")
        
        if scope in ["all", "errors"]:
            self.error_stats.clear()
            reset_items.append("error_stats")
        
        if scope in ["all", "performance"]:
            self.performance_stats = {
                "auth_avg_time": 0.0,
                "config_fetch_avg_time": 0.0,
                "api_response_times": deque(maxlen=100)
            }
            reset_items.append("performance_stats")
        
        if scope in ["all", "hourly"]:
            self._init_hourly_stats()
            reset_items.append("hourly_stats")
        
        logger.bind(tag=TAG).info(f"统计信息已重置: {reset_items}")
        
        return {
            "status": "reset",
            "scope": scope,
            "reset_items": reset_items,
            "reset_time": datetime.now().isoformat()
        }
    
    def _init_hourly_stats(self):
        """初始化小时统计"""
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        # 初始化最近24小时的数据
        for i in range(24):
            hour = current_hour - timedelta(hours=23-i)
            self.hourly_stats.append({
                "hour": hour.isoformat(),
                "total_attempts": 0,
                "successful_auths": 0,
                "failed_auths": 0,
                "mac_auths": 0,
                "token_auths": 0
            })
    
    def _update_hourly_stats(self, success: bool, auth_type: str):
        """更新小时统计"""
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        # 获取当前小时的统计，如果不存在则创建
        if not self.hourly_stats or \
           datetime.fromisoformat(self.hourly_stats[-1]["hour"]) < current_hour:
            # 添加新的小时统计
            self.hourly_stats.append({
                "hour": current_hour.isoformat(),
                "total_attempts": 0,
                "successful_auths": 0,
                "failed_auths": 0,
                "mac_auths": 0,
                "token_auths": 0
            })
        
        # 更新当前小时统计
        current_stat = self.hourly_stats[-1]
        current_stat["total_attempts"] += 1
        
        if success:
            current_stat["successful_auths"] += 1
        else:
            current_stat["failed_auths"] += 1
        
        if auth_type == "mac":
            current_stat["mac_auths"] += 1
        elif auth_type == "token":
            current_stat["token_auths"] += 1
    
    def _update_avg_time(self, metric_name: str, new_time: float, weight: float = 0.1):
        """更新平均时间统计"""
        current_avg = self.performance_stats[metric_name]
        # 使用指数移动平均
        self.performance_stats[metric_name] = current_avg * (1 - weight) + new_time * weight
    
    def _get_top_devices(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取使用量最高的设备"""
        devices = []
        
        for mac_address, stats in self.device_stats.items():
            devices.append({
                "mac_address": mac_address,
                "total_requests": stats["total_requests"],
                "success_rate": (stats["successful_auths"] / max(stats["total_requests"], 1)) * 100,
                "last_access": stats["last_access"],
                "is_registered": stats["is_registered"]
            })
        
        # 按请求数排序
        devices.sort(key=lambda x: x["total_requests"], reverse=True)
        
        return devices[:limit]

# 全局统计管理器实例
_statistics_manager_instance: Optional[StatisticsManager] = None

def get_statistics_manager(config: Dict[str, Any] = None) -> Optional[StatisticsManager]:
    """获取统计管理器实例"""
    global _statistics_manager_instance
    if _statistics_manager_instance is None and config is not None:
        _statistics_manager_instance = StatisticsManager(config)
    return _statistics_manager_instance 