"""
配额管理器
处理MAC地址与使用量管理的集成，提供智能配额控制
"""
import asyncio
import time
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class QuotaExceededError(Exception):
    """配额超限异常"""
    def __init__(self, mac_address: str, quota_type: str, limit: int, used: int):
        self.mac_address = mac_address
        self.quota_type = quota_type
        self.limit = limit
        self.used = used
        super().__init__(f"设备 {mac_address} 的 {quota_type} 配额已用完: {used}/{limit}")

class QuotaManager:
    """配额管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get("enabled", True)
        
        # 配额设置
        self.device_max_output_size = config.get("device_max_output_size", 10000)
        self.daily_quota = config.get("daily_quota", 1000)
        self.hourly_quota = config.get("hourly_quota", 100)
        self.rate_limit_per_minute = config.get("rate_limit_per_minute", 10)
        
        # 使用量统计
        self.usage_stats = defaultdict(lambda: {
            "daily_usage": 0,
            "hourly_usage": 0,
            "minute_usage": 0,
            "last_daily_reset": datetime.now().date(),
            "last_hourly_reset": datetime.now().hour,
            "last_minute_reset": datetime.now().minute,
            "total_requests": 0,
            "total_output_size": 0
        })
        
        # 黑名单管理
        self.blacklisted_devices = set()
        self.violation_counts = defaultdict(int)
        self.max_violations = config.get("max_violations", 5)
        
        logger.bind(tag=TAG).info(f"配额管理器初始化完成, 启用状态: {self.enabled}")
    
    async def check_quota(self, mac_address: str, operation_type: str = "request") -> Tuple[bool, Dict[str, Any]]:
        """检查配额是否允许操作"""
        if not self.enabled:
            return True, {"status": "disabled"}
        
        # 检查黑名单
        if mac_address in self.blacklisted_devices:
            logger.bind(tag=TAG).warning(f"设备在黑名单中: {mac_address}")
            return False, {
                "status": "blacklisted",
                "reason": "设备被列入黑名单",
                "mac_address": mac_address
            }
        
        # 重置计数器
        self._reset_counters_if_needed(mac_address)
        
        stats = self.usage_stats[mac_address]
        
        # 检查分钟级速率限制
        if stats["minute_usage"] >= self.rate_limit_per_minute:
            self._record_violation(mac_address, "rate_limit")
            return False, {
                "status": "rate_limited",
                "reason": f"每分钟请求数超限: {stats['minute_usage']}/{self.rate_limit_per_minute}",
                "retry_after": 60 - datetime.now().second
            }
        
        # 检查小时级配额
        if stats["hourly_usage"] >= self.hourly_quota:
            self._record_violation(mac_address, "hourly_quota")
            return False, {
                "status": "hourly_quota_exceeded",
                "reason": f"小时配额已用完: {stats['hourly_usage']}/{self.hourly_quota}",
                "reset_time": datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            }
        
        # 检查日级配额
        if stats["daily_usage"] >= self.daily_quota:
            self._record_violation(mac_address, "daily_quota")
            return False, {
                "status": "daily_quota_exceeded",
                "reason": f"日配额已用完: {stats['daily_usage']}/{self.daily_quota}",
                "reset_time": datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            }
        
        return True, {
            "status": "allowed",
            "remaining_daily": self.daily_quota - stats["daily_usage"],
            "remaining_hourly": self.hourly_quota - stats["hourly_usage"],
            "remaining_minute": self.rate_limit_per_minute - stats["minute_usage"]
        }
    
    async def record_usage(self, mac_address: str, output_size: int = 0, request_count: int = 1) -> Dict[str, Any]:
        """记录使用量"""
        if not self.enabled:
            return {"status": "disabled"}
        
        # 重置计数器
        self._reset_counters_if_needed(mac_address)
        
        stats = self.usage_stats[mac_address]
        
        # 更新统计
        stats["total_requests"] += request_count
        stats["total_output_size"] += output_size
        stats["daily_usage"] += request_count
        stats["hourly_usage"] += request_count
        stats["minute_usage"] += request_count
        
        # 检查输出大小限制
        if output_size > self.device_max_output_size:
            self._record_violation(mac_address, "output_size")
            logger.bind(tag=TAG).warning(
                f"设备输出大小超限: {mac_address}, 大小: {output_size}/{self.device_max_output_size}"
            )
        
        logger.bind(tag=TAG).debug(
            f"使用量记录: {mac_address}, 请求数: {request_count}, 输出大小: {output_size}"
        )
        
        return {
            "status": "recorded",
            "daily_usage": stats["daily_usage"],
            "hourly_usage": stats["hourly_usage"],
            "total_requests": stats["total_requests"],
            "total_output_size": stats["total_output_size"]
        }
    
    async def check_output_size_limit(self, mac_address: str, proposed_output_size: int) -> Tuple[bool, Dict[str, Any]]:
        """检查输出大小限制"""
        if not self.enabled:
            return True, {"status": "disabled"}
        
        if proposed_output_size > self.device_max_output_size:
            self._record_violation(mac_address, "output_size")
            return False, {
                "status": "output_size_exceeded",
                "reason": f"输出大小超限: {proposed_output_size}/{self.device_max_output_size}",
                "max_allowed_size": self.device_max_output_size
            }
        
        return True, {
            "status": "allowed",
            "max_allowed_size": self.device_max_output_size
        }
    
    def _reset_counters_if_needed(self, mac_address: str):
        """如果需要，重置计数器"""
        stats = self.usage_stats[mac_address]
        current_time = datetime.now()
        
        # 重置日计数器
        if stats["last_daily_reset"] != current_time.date():
            stats["daily_usage"] = 0
            stats["last_daily_reset"] = current_time.date()
            logger.bind(tag=TAG).debug(f"重置日计数器: {mac_address}")
        
        # 重置小时计数器
        if stats["last_hourly_reset"] != current_time.hour:
            stats["hourly_usage"] = 0
            stats["last_hourly_reset"] = current_time.hour
            logger.bind(tag=TAG).debug(f"重置小时计数器: {mac_address}")
        
        # 重置分钟计数器
        if stats["last_minute_reset"] != current_time.minute:
            stats["minute_usage"] = 0
            stats["last_minute_reset"] = current_time.minute
            logger.bind(tag=TAG).debug(f"重置分钟计数器: {mac_address}")
    
    def _record_violation(self, mac_address: str, violation_type: str):
        """记录违规行为"""
        self.violation_counts[mac_address] += 1
        
        logger.bind(tag=TAG).warning(
            f"记录违规: {mac_address}, 类型: {violation_type}, "
            f"累计违规: {self.violation_counts[mac_address]}/{self.max_violations}"
        )
        
        # 检查是否需要加入黑名单
        if self.violation_counts[mac_address] >= self.max_violations:
            self.blacklisted_devices.add(mac_address)
            logger.bind(tag=TAG).error(f"设备加入黑名单: {mac_address}")
    
    async def get_device_usage_stats(self, mac_address: str) -> Dict[str, Any]:
        """获取设备使用量统计"""
        if mac_address not in self.usage_stats:
            return {
                "status": "no_data",
                "mac_address": mac_address
            }
        
        # 重置计数器
        self._reset_counters_if_needed(mac_address)
        
        stats = self.usage_stats[mac_address]
        
        return {
            "status": "success",
            "mac_address": mac_address,
            "daily_usage": stats["daily_usage"],
            "daily_quota": self.daily_quota,
            "daily_remaining": self.daily_quota - stats["daily_usage"],
            "hourly_usage": stats["hourly_usage"],
            "hourly_quota": self.hourly_quota,
            "hourly_remaining": self.hourly_quota - stats["hourly_usage"],
            "minute_usage": stats["minute_usage"],
            "rate_limit": self.rate_limit_per_minute,
            "minute_remaining": self.rate_limit_per_minute - stats["minute_usage"],
            "total_requests": stats["total_requests"],
            "total_output_size": stats["total_output_size"],
            "violation_count": self.violation_counts.get(mac_address, 0),
            "is_blacklisted": mac_address in self.blacklisted_devices
        }
    
    async def enable_device(self, mac_address: str) -> Dict[str, Any]:
        """启用设备（移出黑名单并清除违规记录）"""
        if mac_address in self.blacklisted_devices:
            self.blacklisted_devices.remove(mac_address)

        if mac_address in self.violation_counts:
            del self.violation_counts[mac_address]

        logger.bind(tag=TAG).info(f"设备已启用: {mac_address}")

        return {
            "status": "enabled",
            "mac_address": mac_address,
            "message": "设备已启用，违规记录已清除"
        }
    
    async def disable_device(self, mac_address: str, reason: str = "manual") -> Dict[str, Any]:
        """禁用设备（加入黑名单）"""
        self.blacklisted_devices.add(mac_address)
        logger.bind(tag=TAG).warning(f"设备已禁用: {mac_address}, 原因: {reason}")

        return {
            "status": "disabled",
            "mac_address": mac_address,
            "reason": reason,
            "message": "设备已禁用"
        }
    
    def get_quota_config(self) -> Dict[str, Any]:
        """获取配额配置"""
        return {
            "enabled": self.enabled,
            "device_max_output_size": self.device_max_output_size,
            "daily_quota": self.daily_quota,
            "hourly_quota": self.hourly_quota,
            "rate_limit_per_minute": self.rate_limit_per_minute,
            "max_violations": self.max_violations
        }
    
    def update_quota_config(self, new_config: Dict[str, Any]) -> Dict[str, Any]:
        """更新配额配置"""
        updated_fields = []
        
        for key, value in new_config.items():
            if hasattr(self, key) and getattr(self, key) != value:
                setattr(self, key, value)
                updated_fields.append(key)
        
        logger.bind(tag=TAG).info(f"配额配置已更新: {updated_fields}")
        
        return {
            "status": "updated",
            "updated_fields": updated_fields,
            "new_config": self.get_quota_config()
        }
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        total_devices = len(self.usage_stats)
        total_blacklisted = len(self.blacklisted_devices)
        total_violations = sum(self.violation_counts.values())
        
        total_requests = sum(stats["total_requests"] for stats in self.usage_stats.values())
        total_output_size = sum(stats["total_output_size"] for stats in self.usage_stats.values())
        
        return {
            "total_devices": total_devices,
            "blacklisted_devices": total_blacklisted,
            "total_violations": total_violations,
            "total_requests": total_requests,
            "total_output_size": total_output_size,
            "config": self.get_quota_config()
        }

# 全局配额管理器实例
_quota_manager_instance: Optional[QuotaManager] = None

def get_quota_manager(config: Dict[str, Any] = None) -> Optional[QuotaManager]:
    """获取配额管理器实例"""
    global _quota_manager_instance
    if _quota_manager_instance is None and config is not None:
        _quota_manager_instance = QuotaManager(config)
    return _quota_manager_instance 