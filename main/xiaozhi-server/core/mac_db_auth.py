from core.auth import AuthenticationError
from config.logger import setup_logging
import aiohttp
import time
from typing import Dict, Optional, Set

TAG = __name__
logger = setup_logging()

# 全局缓存变量
_mac_cache: Set[str] = set()  # 已认证的MAC地址缓存
_mac_blacklist: Set[str] = set()  # 黑名单MAC地址缓存
_cache_timestamp: float = 0  # 缓存时间戳
_access_rate_cache: Dict[str, list] = {}  # MAC地址访问频率缓存

# 缓存配置
CACHE_TTL = 300  # 缓存有效期5分钟
ACCESS_LIMIT = 10  # 每分钟最大访问次数
ACCESS_WINDOW = 60  # 访问频率窗口（秒）


class UnregisteredMacError(AuthenticationError):
    """未注册的MAC地址异常"""

    def __init__(self, mac_address):
        self.mac_address = mac_address
        super().__init__(f"未注册的MAC地址: {mac_address}，请前往官网注册您的设备")


def _check_access_rate(mac_address: str) -> bool:
    """检查MAC地址访问频率"""
    global _access_rate_cache
    current_time = time.time()

    # 清理过期记录
    _access_rate_cache = {
        mac: [t for t in times if current_time - t < ACCESS_WINDOW]
        for mac, times in _access_rate_cache.items()
    }

    # 获取当前MAC地址的访问记录
    access_times = _access_rate_cache.get(mac_address, [])

    # 检查是否超过限制
    if len(access_times) >= ACCESS_LIMIT:
        logger.bind(tag=TAG).warning(f"MAC地址 {mac_address} 访问频率过高: {len(access_times)}/{ACCESS_LIMIT}")
        return False

    # 添加当前访问记录
    access_times.append(current_time)
    _access_rate_cache[mac_address] = access_times
    return True


def _is_cache_valid() -> bool:
    """检查缓存是否有效"""
    return time.time() - _cache_timestamp < CACHE_TTL


async def authenticate_mac_address(config: Dict, mac_address: str, client_id: Optional[str] = None) -> Dict:
    """
    带缓存的MAC地址认证函数，减少对后端API的频繁请求

    Args:
        config: 配置字典
        mac_address: MAC地址
        client_id: 客户端ID（可选）

    Returns:
        Dict: 认证结果
        {
            "authenticated": bool,
            "method": str,  # "mac_whitelist", "auto_register", "cache"
            "mac_address": str
        }

    Raises:
        AuthenticationError: 认证失败时抛出异常
    """
    global _mac_cache, _mac_blacklist, _cache_timestamp

    # 1. 检查访问频率限制
    if not _check_access_rate(mac_address):
        raise AuthenticationError("访问频率过高，请稍后再试")

    # 2. 检查本地缓存（如果缓存有效）
    if _is_cache_valid():
        if mac_address in _mac_cache:
            logger.bind(tag=TAG).debug(f"MAC地址缓存命中: {mac_address}")
            return {
                "authenticated": True,
                "method": "cache",
                "mac_address": mac_address
            }

        if mac_address in _mac_blacklist:
            logger.bind(tag=TAG).warning(f"MAC地址在黑名单中: {mac_address}")
            raise AuthenticationError("设备已被禁用")

    # 3. 缓存未命中或已过期，调用后端API
    try:
        # 获取API配置
        manager_api_config = config.get("manager-api", {})
        api_base_url = manager_api_config.get("url")
        api_key = manager_api_config.get("secret")

        if not api_base_url or not api_key:
            raise AuthenticationError("API配置缺失")

        # 准备请求数据
        request_data = {
            "macAddress": mac_address,
            "clientId": client_id
        }

        # 调用Java后端的统一认证API
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=10)

            async with session.post(
                f"{api_base_url}/device/auth/authenticate",
                headers=headers,
                json=request_data,
                timeout=timeout
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("code") == 0:
                        result = data.get("data", {})

                        # 4. 更新缓存
                        if result.get("authenticated"):
                            _mac_cache.add(mac_address)
                            logger.bind(tag=TAG).info(f"MAC认证成功并缓存: {mac_address}, 方法: {result.get('method')}")
                        else:
                            # 认证失败，可能需要加入黑名单（根据具体错误信息判断）
                            error_msg = data.get("msg", "")
                            if "禁用" in error_msg or "黑名单" in error_msg:
                                _mac_blacklist.add(mac_address)
                                logger.bind(tag=TAG).warning(f"MAC地址加入黑名单缓存: {mac_address}")

                        # 更新缓存时间戳
                        _cache_timestamp = time.time()

                        return result
                    else:
                        error_msg = data.get("msg", "认证失败")
                        logger.bind(tag=TAG).warning(f"MAC认证失败: {mac_address}, 原因: {error_msg}")

                        # 根据错误信息决定是否加入黑名单缓存
                        if "禁用" in error_msg or "黑名单" in error_msg:
                            _mac_blacklist.add(mac_address)
                            _cache_timestamp = time.time()

                        raise AuthenticationError(f"MAC地址认证失败: {error_msg}")
                else:
                    logger.bind(tag=TAG).error(f"MAC认证API请求失败: {response.status}")
                    raise AuthenticationError("MAC地址认证服务不可用")

    except aiohttp.ClientError as e:
        logger.bind(tag=TAG).error(f"MAC认证网络错误: {e}")
        raise AuthenticationError("MAC地址认证网络错误")
    except Exception as e:
        logger.bind(tag=TAG).error(f"MAC认证异常: {e}")
        raise AuthenticationError(f"MAC地址认证异常: {e}")


async def refresh_mac_cache(config: Dict) -> None:
    """
    主动刷新MAC地址缓存（可选功能）

    Args:
        config: 配置字典
    """
    global _mac_cache, _mac_blacklist, _cache_timestamp

    try:
        # 获取API配置
        manager_api_config = config.get("manager-api", {})
        api_base_url = manager_api_config.get("url")
        api_key = manager_api_config.get("secret")

        if not api_base_url or not api_key:
            logger.bind(tag=TAG).warning("API配置缺失，无法刷新缓存")
            return

        # 调用后端API获取MAC白名单和黑名单
        async with aiohttp.ClientSession() as session:
            headers = {"Authorization": f"Bearer {api_key}"}
            timeout = aiohttp.ClientTimeout(total=30)

            # 获取启用设备的MAC地址列表（替代白名单）
            try:
                async with session.get(
                    f"{api_base_url}/device/mac/enabled",
                    headers=headers,
                    timeout=timeout
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0 and isinstance(data.get("data"), list):
                            _mac_cache = set(data["data"])
                            logger.bind(tag=TAG).info(f"刷新启用设备MAC缓存成功，共 {len(_mac_cache)} 个设备")
            except Exception as e:
                logger.bind(tag=TAG).error(f"刷新启用设备MAC缓存失败: {e}")

            # 获取黑名单
            try:
                async with session.get(
                    f"{api_base_url}/device/mac/blacklist",
                    headers=headers,
                    timeout=timeout
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0 and isinstance(data.get("data"), list):
                            _mac_blacklist = set(data["data"])
                            logger.bind(tag=TAG).info(f"刷新MAC黑名单缓存成功，共 {len(_mac_blacklist)} 个设备")
            except Exception as e:
                logger.bind(tag=TAG).error(f"刷新MAC黑名单缓存失败: {e}")

            # 更新缓存时间戳
            _cache_timestamp = time.time()

    except Exception as e:
        logger.bind(tag=TAG).error(f"刷新MAC缓存异常: {e}")


def clear_mac_cache() -> None:
    """清空MAC地址缓存"""
    global _mac_cache, _mac_blacklist, _cache_timestamp, _access_rate_cache

    _mac_cache.clear()
    _mac_blacklist.clear()
    _cache_timestamp = 0
    _access_rate_cache.clear()

    logger.bind(tag=TAG).info("设备认证缓存已清空")


def get_cache_stats() -> Dict:
    """获取缓存统计信息"""
    return {
        "enabled_devices_count": len(_mac_cache),
        "blacklist_count": len(_mac_blacklist),
        "cache_valid": _is_cache_valid(),
        "cache_age_seconds": time.time() - _cache_timestamp if _cache_timestamp > 0 else 0,
        "access_rate_entries": len(_access_rate_cache)
    }