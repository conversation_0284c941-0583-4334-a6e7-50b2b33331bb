#!/usr/bin/env python3
"""
清理MAC地址认证缓存的脚本
用于解决MAC地址认证缓存不一致的问题
"""

import requests
import sys

def clear_mac_cache(mac_address, api_base_url, api_key):
    """清理指定MAC地址的认证缓存"""
    
    # 1. 清理Redis中的MAC认证缓存
    cache_url = f"{api_base_url}/device/auth/invalidate/{mac_address}"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(cache_url, headers=headers, timeout=10)
        if response.status_code == 200:
            print(f"✅ 成功清理MAC地址 {mac_address} 的Redis缓存")
        else:
            print(f"❌ 清理Redis缓存失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 清理Redis缓存异常: {e}")
    
    # 2. 测试MAC地址认证
    test_url = f"{api_base_url}/device/auth/check/{mac_address}"
    try:
        response = requests.get(test_url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                is_enabled = data.get("data", False)
                print(f"✅ MAC地址 {mac_address} 认证状态: {'启用' if is_enabled else '未启用'}")
            else:
                print(f"❌ 认证检查失败: {data.get('msg', '未知错误')}")
        else:
            print(f"❌ 认证检查请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 认证检查异常: {e}")

def main():
    # 配置信息
    MAC_ADDRESS = "94:a9:90:29:06:10"  # 问题MAC地址
    API_BASE_URL = "http://**************:8002"  # 测试服务器API地址
    API_KEY = "your_api_key_here"  # 需要替换为实际的API密钥
    
    print(f"🔧 开始清理MAC地址认证缓存: {MAC_ADDRESS}")
    print(f"📡 API地址: {API_BASE_URL}")
    
    if API_KEY == "your_api_key_here":
        print("❌ 请先配置正确的API密钥")
        sys.exit(1)
    
    clear_mac_cache(MAC_ADDRESS, API_BASE_URL, API_KEY)
    
    print("\n🎯 建议操作:")
    print("1. 重启xiaozhi-server服务以清理Python端缓存")
    print("2. 重新测试设备连接")
    print("3. 检查manager-api日志确认认证流程")

if __name__ == "__main__":
    main()
