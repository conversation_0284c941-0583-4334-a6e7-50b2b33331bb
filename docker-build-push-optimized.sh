#!/bin/bash
# docker-build-push-optimized.sh
# 优化版Docker构建推送脚本 - 支持架构特定的缓存优化
# 基于原docker-build-push.sh，增加Mac M1 → AMD64跨平台构建缓存优化

# 优化组件目录
OPTIMIZATION_DIR="docker-cache-optimization"

# 加载环境变量
ENV_FILE=".env"
ENV_TEMPLATE=".env.template"

if [ -f "$ENV_FILE" ]; then
  source "$ENV_FILE"
else
  echo "错误: 环境配置文件 $ENV_FILE 不存在"
  echo "请复制 $ENV_TEMPLATE 为 $ENV_FILE 并填写您的配置"
  exit 1
fi

# 终端颜色设置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的信息
info() { echo -e "${GREEN}[INFO] $1${NC}" >&2; }
warn() { echo -e "${YELLOW}[WARN] $1${NC}" >&2; }
error() { echo -e "${RED}[ERROR] $1${NC}" >&2; }

# 注意：清理功能已移至独立的 docker-cleanup.sh 脚本
# 请使用 ./docker-cleanup.sh 进行Docker资源清理

# 脚本标题
echo -e "${BLUE}==========================================${NC}"
echo -e "${BLUE}  小智ESP32服务器 优化构建与推送脚本  ${NC}"
echo -e "${BLUE}==========================================${NC}"

# 配置变量（继承原有配置）
DOCKER_REGISTRY="${DOCKER_REGISTRY:-xiaozhi-esp32-server}"
GITHUB_REGISTRY="ghcr.io/matatalab"
DOCKERHUB_REGISTRY="docker.io"
IMAGE_PREFIX="${IMAGE_PREFIX:-xiaozhi-esp32-server}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
BUILD_SERVER="${BUILD_SERVER:-false}"
BUILD_WEB="${BUILD_WEB:-false}"
BUILD_ALL="${BUILD_ALL:-true}"
PUSH_IMAGES="${PUSH_IMAGES:-true}"
PUSH_PERSONAL="${PUSH_PERSONAL:-true}"
PUSH_GITHUB="${PUSH_GITHUB:-false}"
PUSH_DOCKERHUB="${PUSH_DOCKERHUB:-false}"
SKIP_PRE_CLEAN="${SKIP_PRE_CLEAN:-false}"
AUTO_CONFIRM="${AUTO_CONFIRM:-false}"

# 新增：缓存优化配置
USE_CACHE_OPTIMIZATION="${USE_CACHE_OPTIMIZATION:-true}"
FORCE_NO_CACHE="${FORCE_NO_CACHE:-false}"  # 默认不强制禁用构建缓存
REBUILD_BASE_IMAGE="${REBUILD_BASE_IMAGE:-false}"  # 默认不强制重建基础镜像
FORCE_USE_PROXY="${FORCE_USE_PROXY:-false}"  # 默认不强制使用代理
FORCE_USE_MIRROR="${FORCE_USE_MIRROR:-false}"  # 默认不强制使用镜像源
PROXY_HOST="${PROXY_HOST:-127.0.0.1}"  # 默认代理主机
HTTP_PROXY_PORT="${HTTP_PROXY_PORT:-6152}"  # 默认HTTP代理端口
SOCKS_PROXY_PORT="${SOCKS_PROXY_PORT:-6153}"  # 默认SOCKS代理端口
QUICK_MODE="${QUICK_MODE:-false}"  # 默认不使用快速模式

# 启用BuildKit
export DOCKER_BUILDKIT=1

# 个人服务器配置
PERSONAL_SERVER_IDS=("prod" "test")
PERSONAL_SERVER_PROD="正式环境:${PROD_HOST}:${PROD_CERT_PATH}"
PERSONAL_SERVER_TEST="测试环境:${TEST_HOST}:${TEST_CERT_PATH}"
SELECTED_PERSONAL_SERVERS=()

# 镜像源配置 - 直接使用chsrc中定义的完整镜像源列表
DEBIAN_MIRRORS=(
    # 上游默认源
    "http://deb.debian.org/debian/"
    # MirrorZ 校园网镜像站
    "https://mirrors.cernet.edu.cn/debian/"
    # 阿里巴巴开源镜像站(公网)
    "https://mirrors.aliyun.com/debian/"
    # 火山引擎开源软件镜像站(公网)
    "https://mirrors.volces.com/debian/"
    # 北京外国语大学开源软件镜像站
    "https://mirrors.bfsu.edu.cn/debian/"
    # 中国科学技术大学开源镜像站
    "https://mirrors.ustc.edu.cn/debian/"
    # 清华大学开源软件镜像站
    "https://mirrors.tuna.tsinghua.edu.cn/debian/"
    # 腾讯软件源(公网)
    "https://mirrors.tencent.com/debian/"
    # 网易开源镜像站
    "https://mirrors.163.com/debian/"
    # 搜狐开源镜像站
    "https://mirrors.sohu.com/debian/"
)

PYPI_MIRRORS=(
    # 上游默认源
    "https://pypi.org/simple/"
    # 北京外国语大学开源软件镜像站
    "https://mirrors.bfsu.edu.cn/pypi/web/simple/"
    # 兰州大学开源社区镜像站
    "https://mirror.lzu.edu.cn/pypi/web/simple/"
    # 吉林大学开源镜像站
    "https://mirrors.jlu.edu.cn/pypi/web/simple/"
    # 上海交通大学致远镜像站
    "https://mirror.sjtu.edu.cn/pypi/web/simple/"
    # 清华大学开源软件镜像站
    "https://pypi.tuna.tsinghua.edu.cn/simple/"
    # 阿里巴巴开源镜像站(公网)
    "https://mirrors.aliyun.com/pypi/simple/"
    # 南京大学开源镜像站
    "https://mirror.nju.edu.cn/pypi/web/simple/"
    # 北京大学开源镜像站
    "https://mirrors.pku.edu.cn/pypi/web/simple/"
    # 腾讯软件源(公网)
    "https://mirrors.cloud.tencent.com/pypi/simple/"
    # 华为开源镜像站
    "https://mirrors.huaweicloud.com/repository/pypi/simple/"
    # 华中科技大学开源镜像站
    "https://mirrors.hust.edu.cn/pypi/web/simple/"
)

NPM_MIRRORS=(
    # 上游默认源
    "https://registry.npmjs.org/"
    # npmmirror (阿里云赞助)
    "https://registry.npmmirror.com/"
    # 华为开源镜像站
    "https://mirrors.huaweicloud.com/repository/npm/"
    # 腾讯软件源(公网)
    "https://mirrors.cloud.tencent.com/npm/"
)

# 将字节速度转换为人类可读格式 - 参考chsrc项目的实现
to_human_readable_speed() {
    local speed="$1"  # bytes/s

    if [[ ! "$speed" =~ ^[0-9]+\.?[0-9]*$ ]] || [[ "$speed" == "0" ]]; then
        echo "0 B/s"
        return
    fi

    # 使用awk进行浮点数计算
    echo "$speed" | awk '
    {
        speed = $1
        scale[0] = "B/s"
        scale[1] = "KB/s"
        scale[2] = "MB/s"
        scale[3] = "GB/s"
        scale[4] = "TB/s"

        i = 0
        while (speed > 1024.0 && i < 4) {
            i++
            speed = speed / 1024.0
        }

        printf "%.2f %s", speed, scale[i]
    }'
}

# 测试镜像源下载速度 - 参考chsrc项目的实现
test_mirror_speed() {
    local mirror_url="$1"
    local mirror_type="$2"
    local debug="${3:-false}"  # 第三个参数控制是否显示调试信息
    local timeout=8  # 参考chsrc的默认8秒超时

    # 根据镜像源类型选择合适的测试文件
    local test_url=""

    case "$mirror_type" in
        "pypi")
            # PyPI: 测试simple页面，这是最可靠的方法
            if [[ "$mirror_url" == *"/simple/pip/"* ]]; then
                # 如果URL已经包含完整路径，直接使用
                test_url="$mirror_url"
            elif [[ "$mirror_url" == *"/simple/" ]]; then
                # 如果URL以/simple/结尾，添加pip/
                test_url="${mirror_url}pip/"
            else
                # 否则添加完整路径
                test_url="${mirror_url%/}/simple/pip/"
            fi
            ;;
        "npm")
            # NPM: 测试registry根路径或下载小文件
            if [[ "$mirror_url" == *"registry.npmmirror.com"* ]]; then
                test_url="${mirror_url%/}/lodash/-/lodash-4.17.21.tgz"
            elif [[ "$mirror_url" == *"registry.npmjs.org"* ]]; then
                test_url="${mirror_url%/}/lodash/-/lodash-4.17.21.tgz"
            else
                # 对于其他镜像源，尝试直接访问根路径
                test_url="${mirror_url%/}/"
            fi
            ;;
        "debian")
            # Debian: 下载Release文件
            if [[ "$mirror_url" == *"/dists/"* ]]; then
                test_url="$mirror_url"
            else
                test_url="${mirror_url%/}/dists/stable/Release"
            fi
            ;;
    esac

    # 使用IPv6选项（如果启用）
    local ipv6_option=""
    if [[ "${USE_IPV6:-false}" == "true" ]]; then
        ipv6_option="--ipv6"
    fi

    # 执行curl测速，简化命令避免复杂的引号问题
    local curl_result
    local curl_exit_code

    # 调试信息
    if [[ "$debug" == "true" ]]; then
        echo "[DEBUG] 测试URL: $test_url" >&2
    fi

    # 直接执行curl命令，避免bash -c的复杂性，增加超时时间
    local curl_timeout=15  # 增加curl超时时间
    if [[ "${USE_IPV6:-false}" == "true" ]]; then
        curl_result=$(curl --ipv6 -sL -o /dev/null -w "%{http_code} %{speed_download}" -m$curl_timeout -A "chsrc-docker-build/1.0" "$test_url" 2>/dev/null)
        curl_exit_code=$?
    else
        curl_result=$(curl -sL -o /dev/null -w "%{http_code} %{speed_download}" -m$curl_timeout -A "chsrc-docker-build/1.0" "$test_url" 2>/dev/null)
        curl_exit_code=$?
    fi

    # 如果curl命令失败，设置默认值
    if [[ $curl_exit_code -ne 0 ]] || [[ -z "$curl_result" ]]; then
        curl_result="000 0"
    fi

    # 解析结果：分隔HTTP状态码和下载速度，移除可能的引号
    # 移除引号并确保正确解析
    curl_result=$(echo "$curl_result" | sed "s/['\"]//g")
    local http_code speed_download
    if [[ "$curl_result" =~ ^([0-9]+)[[:space:]]+([0-9.]+)$ ]]; then
        http_code="${BASH_REMATCH[1]}"
        speed_download="${BASH_REMATCH[2]}"
    else
        # 如果正则匹配失败，尝试简单的空格分割
        read -r http_code speed_download <<< "$curl_result"
    fi

    # 调试信息
    if [[ "$debug" == "true" ]]; then
        echo "[DEBUG] curl退出码: $curl_exit_code" >&2
        echo "[DEBUG] curl原始结果: '$curl_result'" >&2
        echo "[DEBUG] HTTP状态码: $http_code" >&2
        echo "[DEBUG] 下载速度: $speed_download bytes/s" >&2
    fi

    # 处理结果 - 更宽松的判断逻辑
    if [[ "$debug" == "true" ]]; then
        echo "[DEBUG] 开始处理结果..." >&2
    fi

    # 如果curl完全失败
    if [[ $curl_exit_code -eq 124 ]]; then
        if [[ "$debug" == "true" ]]; then
            echo "[DEBUG] 超时失败" >&2
        fi
        echo "-1"
        return
    fi

    # 如果没有获取到任何结果
    if [[ -z "$http_code" ]] || [[ -z "$speed_download" ]]; then
        if [[ "$debug" == "true" ]]; then
            echo "[DEBUG] 没有获取到结果" >&2
        fi
        echo "-1"
        return
    fi

    # 检查HTTP状态码是否有效
    if [[ ! "$http_code" =~ ^[0-9]+$ ]]; then
        if [[ "$debug" == "true" ]]; then
            echo "[DEBUG] HTTP状态码无效: $http_code" >&2
        fi
        echo "-1"
        return
    fi

    # 根据镜像源类型判断状态码是否正常 - 更宽松的判断
    local is_success=false
    case "$mirror_type" in
        "pypi")
            # PyPI: 200 OK, 403 Forbidden都算正常
            if [[ "$http_code" -eq 200 ]] || [[ "$http_code" -eq 403 ]]; then
                is_success=true
            fi
            ;;
        "npm"|"debian")
            # NPM和Debian: 2xx状态码算正常，即使速度为0也接受
            if [[ "$http_code" -ge 200 && "$http_code" -lt 300 ]]; then
                is_success=true
            fi
            ;;
    esac

    if [[ "$debug" == "true" ]]; then
        echo "[DEBUG] 状态码检查结果: is_success=$is_success" >&2
    fi

    if [[ "$is_success" == "true" ]]; then
        # 检查速度值
        if [[ "$speed_download" =~ ^[0-9]+\.?[0-9]*$ ]]; then
            # 即使速度为0也返回，让上层决定如何处理
            echo "${speed_download%.*}"
        else
            if [[ "$debug" == "true" ]]; then
                echo "[DEBUG] 速度值格式无效: $speed_download" >&2
            fi
            echo "-1"
        fi
    else
        if [[ "$debug" == "true" ]]; then
            echo "[DEBUG] HTTP状态码不符合要求: $http_code" >&2
        fi
        echo "-1"
    fi
}

# 选择最佳镜像源 - 实时显示版本
select_best_mirror() {
    local mirror_type="$1"
    local mirrors=()
    local best_mirror=""
    local best_speed=0
    local results_array=()

    case "$mirror_type" in
        "debian")
            mirrors=("${DEBIAN_MIRRORS[@]}")
            ;;
        "pypi")
            mirrors=("${PYPI_MIRRORS[@]}")
            ;;
        "npm")
            mirrors=("${NPM_MIRRORS[@]}")
            ;;
        *)
            error "未知的镜像源类型: $mirror_type"
            return 1
            ;;
    esac

    info "正在测试 $mirror_type 镜像源速度..."
    echo "-----------------------------------"

    # 显示测试方法说明
    case "$mirror_type" in
        "pypi")
            echo "测试方法: 访问 /simple/pip/ 页面 (PyPI标准API)"
            ;;
        "npm")
            echo "测试方法: 下载 lodash-4.17.21.tgz 或访问根路径"
            ;;
        "debian")
            echo "测试方法: 下载 Release 文件 (~10KB)"
            ;;
    esac

    echo ""

    # 同步测试每个镜像源，实时显示结果
    local index=1
    for mirror in "${mirrors[@]}"; do
        local debug_mode="${DEBUG_MIRRORS:-false}"

        # 显示正在测试的镜像源，参考chsrc的格式
        printf "  - 镜像源 %d (%s) ... " "$index" "$mirror"

        # 强制刷新输出缓冲区，确保实时显示
        if command -v flush >/dev/null 2>&1; then
            flush
        fi

        # 执行测试
        if [[ "$debug_mode" == "true" ]]; then
            echo "[DEBUG] 开始测试镜像源: $mirror" >&2
        fi

        local speed_bytes=$(test_mirror_speed "$mirror" "$mirror_type" "$debug_mode")

        if [[ "$debug_mode" == "true" ]]; then
            echo "[DEBUG] 测试结果: $speed_bytes" >&2
        fi

        # 检查返回值是否为数字
        if [[ ! "$speed_bytes" =~ ^-?[0-9]+$ ]]; then
            if [[ "$debug_mode" == "true" ]]; then
                echo "[DEBUG] 返回值不是数字: '$speed_bytes'" >&2
            fi
            speed_bytes=-1
        fi

        if [[ $speed_bytes -eq -1 ]]; then
            echo "0.00 Byte/s | 测试失败"
        else
            local speed_readable=$(to_human_readable_speed "$speed_bytes")
            echo "$speed_readable"

            # 保存结果用于排序
            results_array+=("$speed_bytes:$mirror")

            # 更新最佳镜像源
            if [[ $speed_bytes -gt $best_speed ]]; then
                best_speed=$speed_bytes
                best_mirror=$mirror
            fi
        fi

        ((index++))
    done

    echo ""
    echo "-----------------------------------"

    # 显示排名
    if [[ -n "$best_mirror" ]]; then
        if [[ ${#results_array[@]} -gt 0 ]]; then
            # 根据下载速度排序（降序，速度越高越好）
            IFS=$'\n' sorted_results=($(sort -t: -nr -k1,1 <<< "${results_array[*]}"))

            info "最佳 $mirror_type 镜像源排名:"
            local rank=1
            for result in "${sorted_results[@]}"; do
                IFS=: read -r speed_bytes mirror <<< "$result"
                local speed_readable=$(to_human_readable_speed "$speed_bytes")
                if [[ $rank -eq 1 ]]; then
                    echo "  🥇 第1名: $mirror ($speed_readable) [已选择]"
                elif [[ $rank -eq 2 ]]; then
                    echo "  🥈 第2名: $mirror ($speed_readable)"
                elif [[ $rank -eq 3 ]]; then
                    echo "  🥉 第3名: $mirror ($speed_readable)"
                fi
                ((rank++))

                # 只显示前3名
                if [[ $rank -gt 3 ]]; then
                    break
                fi
            done
        fi

        # 将最佳镜像源写入临时文件，避免echo被捕获
        echo "$best_mirror" > "/tmp/best_mirror_${mirror_type}_$$"
    else
        warn "所有 $mirror_type 镜像源均不可用或测试超时"
        # 返回默认镜像源
        case "$mirror_type" in
            "debian")
                echo "http://mirrors.163.com/debian/" > "/tmp/best_mirror_${mirror_type}_$$"
                warn "使用默认Debian镜像源: http://mirrors.163.com/debian/"
                ;;
            "pypi")
                echo "https://pypi.douban.com/simple/" > "/tmp/best_mirror_${mirror_type}_$$"
                warn "使用默认PyPI镜像源: https://pypi.douban.com/simple/"
                ;;
            "npm")
                echo "https://registry.npmmirror.com" > "/tmp/best_mirror_${mirror_type}_$$"
                warn "使用默认NPM镜像源: https://registry.npmmirror.com"
                ;;
        esac
    fi
}

# 显示帮助信息
show_help() {
  echo "小智ESP32服务器Docker镜像构建与推送工具（优化版）"
  echo ""
  echo "用法: $0 [选项]"
  echo ""
  echo "构建架构:"
  echo "  --target-amd64           指定目标平台为linux/amd64"
  echo "  --target-arm64           指定目标平台为linux/arm64"
  echo ""
  echo "缓存优化选项:"
  echo "  --no-cache-optimization  禁用缓存优化"
  echo "  --force-no-cache         完全禁用Docker构建缓存，强制全新构建"
  echo "  --rebuild-base-image     强制重新构建基础依赖镜像（即使已存在）"
  echo ""
  echo "网络选项:"
  echo "  --force-proxy            强制使用代理（默认会自动检测）"
  echo "  --force-mirror           强制使用镜像源（默认会自动检测）"
  echo "  --proxy-host <主机>      设置代理主机地址（默认: 127.0.0.1）"
  echo "  --http-proxy-port <端口> 设置HTTP代理端口（默认: 6152）"
  echo "  --socks-proxy-port <端口> 设置SOCKS代理端口（默认: 6153）"
  echo "  --quick                  快速模式，跳过镜像源测速直接使用默认源"
  echo "  --test-mirrors           仅测试镜像源速度，不进行构建"
  echo "  --debug-mirrors          调试模式测试镜像源（显示详细信息）"
  echo "  --test-url <URL>         测试单个URL的下载速度"
  echo ""
  echo "选项:"
  echo "  -h, --help              显示帮助信息"
  echo "  -r, --registry <地址>    设置Docker镜像仓库地址"
  echo "  -p, --prefix <前缀>      设置镜像名称前缀"
  echo "  -t, --tag <标签>         设置镜像标签"
  echo "  --all                   构建所有镜像（默认选项）"
  echo "  --server-only           仅构建服务器镜像"
  echo "  --web-only              仅构建Web界面镜像"
  echo "  --no-push               构建镜像但不推送到仓库"
  echo "  --push-personal         交互式选择并推送到个人服务器"
  echo "  --push-github           推送到GitHub Container Registry"
  echo "  --push-dockerhub        推送到DockerHub, 使用 --dockerhub-user 指定用户名"
  echo "  --dockerhub-user <用户名> 设置推送到DockerHub的用户名（与--push-dockerhub一起使用）"
  echo "  --skip-pre-clean        跳过构建前的清理（失败重试时有用）"
  echo "  -y, --yes               自动确认所有提示，不进行交互"
  echo ""
  echo "缓存优化说明:"
  echo "  本脚本使用基础依赖镜像作为缓存优化策略，提高构建速度。"
  echo "  基础依赖镜像会被创建并保存为 <前缀>-base:<架构>"
  echo "  随后的构建将直接使用这些基础镜像，无需重新安装依赖"
  echo ""
  echo "网络优化说明:"
  echo "  脚本会自动测试所有可用的镜像源，选择速度最快的"
  echo "  如果检测到代理可用，会优先使用代理"
  echo "  可以使用--force-proxy或--force-mirror强制选择网络模式"
  echo ""
  echo "Docker清理:"
  echo "  清理功能已移至独立脚本 docker-cleanup.sh"
  echo "  使用方法: ./docker-cleanup.sh [light|full|xiaozhi|system]"
  echo ""
  echo "示例:"
  echo "  $0 --target-amd64 --no-push        # 只构建amd64镜像，不推送"
  echo "  $0 --force-no-cache               # 完全禁用缓存进行构建"
  echo "  $0 --server-only                   # 只构建服务器镜像（本地架构）"
  echo "  $0 --rebuild-base-image            # 强制重新构建基础依赖镜像"
  echo "  $0 --force-proxy                   # 强制使用代理模式"
  echo "  $0 --force-mirror                  # 强制使用镜像源模式"
  echo "  $0 --test-mirrors                  # 仅测试镜像源速度"
  echo "  ./docker-cleanup.sh full          # 构建后执行完全清理"
  echo ""
}

# 检测构建场景
detect_build_scenario() {
    local host_arch=$(uname -m)

    case $host_arch in
        "x86_64"|"amd64") host_arch="amd64" ;;
        "aarch64"|"arm64") host_arch="arm64" ;;
    esac

    # 检查是否已经设置了目标平台
    if [[ -z "${TARGET_PLATFORM:-}" ]]; then
        # 如果没有设置TARGET_PLATFORM，则尝试使用DOCKER_DEFAULT_PLATFORM
    if [[ -n "${DOCKER_DEFAULT_PLATFORM:-}" ]]; then
            export TARGET_PLATFORM="${DOCKER_DEFAULT_PLATFORM}"
    else
            # 没有设置任何平台，使用本地架构
            export TARGET_PLATFORM="linux/$host_arch"
        fi
    fi

    info "目标构建平台: ${TARGET_PLATFORM}"

    # 从平台字符串中提取架构
    local target_arch=$(echo "$TARGET_PLATFORM" | cut -d'/' -f2)

    export HOST_ARCH="$host_arch"
    export TARGET_ARCH="$target_arch"

    # 判断构建场景
    if [[ "$target_arch" == "$host_arch" ]]; then
        export BUILD_SCENARIO="local_debug"
        export IS_CROSS_BUILD="false"
        info "检测到本地调试场景: $host_arch → $target_arch (原生构建)"
    else
        export BUILD_SCENARIO="server_deploy"
        export IS_CROSS_BUILD="true"

        if [[ "$host_arch" == "arm64" && "$target_arch" == "amd64" ]]; then
            export CROSS_BUILD_STRATEGY="mac_m1_to_amd64"
            info "检测到服务器部署场景: Mac M1 → AMD64，启用跨架构缓存优化"
        else
            export CROSS_BUILD_STRATEGY="generic_cross"
            info "检测到跨平台构建: $host_arch → $target_arch"
        fi
    fi

    # 显示架构缓存隔离信息
    if [[ "$USE_CACHE_OPTIMIZATION" == "true" ]]; then
        info "缓存隔离: $host_arch 和 $target_arch 将使用独立的缓存目录"
    fi
}

# 保存原始参数数量（在参数解析之前）
ORIGINAL_ARG_COUNT=$#

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --target-amd64)
            export DOCKER_DEFAULT_PLATFORM="linux/amd64"
            export TARGET_PLATFORM="linux/amd64"
            shift
            ;;
        --target-arm64)
            export DOCKER_DEFAULT_PLATFORM="linux/arm64"
            export TARGET_PLATFORM="linux/arm64"
            shift
            ;;
        --no-cache-optimization)
            USE_CACHE_OPTIMIZATION=false
            shift
            ;;
        --force-no-cache)
            FORCE_NO_CACHE=true
            shift
            ;;
        --rebuild-base-image)
            REBUILD_BASE_IMAGE=true
            shift
            ;;
        --force-proxy)
            FORCE_USE_PROXY=true
            FORCE_USE_MIRROR=false
            shift
            ;;
        --force-mirror)
            FORCE_USE_PROXY=false
            FORCE_USE_MIRROR=true
            shift
            ;;
        --proxy-host)
            PROXY_HOST="$2"
            shift 2
            ;;
        --http-proxy-port)
            HTTP_PROXY_PORT="$2"
            shift 2
            ;;
        --socks-proxy-port)
            SOCKS_PROXY_PORT="$2"
            shift 2
            ;;

        -r|--registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        -p|--prefix)
            IMAGE_PREFIX="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --server-only)
            BUILD_SERVER=true
            BUILD_WEB=false
            BUILD_ALL=false
            shift
            ;;
        --web-only)
            BUILD_SERVER=false
            BUILD_WEB=true
            BUILD_ALL=false
            shift
            ;;
        --all)
            BUILD_ALL=true
            BUILD_SERVER=false
            BUILD_WEB=false
            shift
            ;;
        --no-push)
            PUSH_IMAGES=false
            PUSH_PERSONAL=false
            PUSH_GITHUB=false
            PUSH_DOCKERHUB=false
            shift
            ;;
        --push-personal)
            PUSH_PERSONAL=true
            PUSH_IMAGES=true
            shift
            ;;
        --push-github)
            PUSH_GITHUB=true
            PUSH_IMAGES=true
            shift
            ;;
        --push-dockerhub)
            PUSH_DOCKERHUB=true
            PUSH_IMAGES=true
            shift
            ;;
        --dockerhub-user)
            DOCKERHUB_USERNAME="$2"
            shift 2
            ;;
        --skip-pre-clean)
            SKIP_PRE_CLEAN=true
            SKIP_PRE_CLEAN_SPECIFIED=true
            shift
            ;;
        -y|--yes)
            AUTO_CONFIRM=true
            shift
            ;;
        --quick)
            QUICK_MODE=true
            shift
            ;;
        --test-mirrors)
            TEST_MIRRORS_ONLY=true
            shift
            ;;
        --debug-mirrors)
            TEST_MIRRORS_ONLY=true
            DEBUG_MIRRORS=true
            shift
            ;;
        --test-url)
            TEST_SINGLE_URL="$2"
            shift 2
            ;;
        *)
            error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证Docker是否安装
if ! command -v docker &> /dev/null; then
    error "Docker未安装，请先安装Docker!"
    exit 1
fi

# 如果使用DockerHub，验证是否有用户名设置
if [ "$PUSH_DOCKERHUB" = true ] && [ -z "$DOCKERHUB_USERNAME" ]; then
    error "使用--push-dockerhub选项时必须指定--dockerhub-user参数"
    exit 1
fi

# 如果是测试单个URL
if [ -n "$TEST_SINGLE_URL" ]; then
    info "测试单个URL: $TEST_SINGLE_URL"
    echo ""

    # 简单判断URL类型
    url_type="unknown"
    if [[ "$TEST_SINGLE_URL" == *"pypi"* ]] || [[ "$TEST_SINGLE_URL" == *"simple"* ]]; then
        url_type="pypi"
    elif [[ "$TEST_SINGLE_URL" == *"npm"* ]] || [[ "$TEST_SINGLE_URL" == *"registry"* ]]; then
        url_type="npm"
    elif [[ "$TEST_SINGLE_URL" == *"debian"* ]] || [[ "$TEST_SINGLE_URL" == *"dists"* ]]; then
        url_type="debian"
    fi

    echo "检测到URL类型: $url_type"
    echo "开始测试..."

    speed_result=$(test_mirror_speed "$TEST_SINGLE_URL" "$url_type" "true")

    if [[ "$speed_result" == "-1" ]]; then
        error "测试失败！"
        exit 1
    else
        speed_readable=$(to_human_readable_speed "$speed_result")
        info "测试成功！下载速度: $speed_readable"
        exit 0
    fi
fi

# 如果只是测试镜像源，执行测试后退出
if [ "$TEST_MIRRORS_ONLY" = true ]; then
    info "开始测试所有镜像源..."
    echo ""

    info "测试Debian镜像源..."
    select_best_mirror "debian"
    debian_result=$(cat "/tmp/best_mirror_debian_$$" 2>/dev/null || echo "未知")
    echo ""

    info "测试PyPI镜像源..."
    select_best_mirror "pypi"
    pypi_result=$(cat "/tmp/best_mirror_pypi_$$" 2>/dev/null || echo "未知")
    echo ""

    info "测试NPM镜像源..."
    select_best_mirror "npm"
    npm_result=$(cat "/tmp/best_mirror_npm_$$" 2>/dev/null || echo "未知")
    echo ""

    info "测试完成！推荐的镜像源："
    echo "  Debian: $debian_result"
    echo "  PyPI: $pypi_result"
    echo "  NPM: $npm_result"

    # 清理临时文件
    rm -f "/tmp/best_mirror_debian_$$" "/tmp/best_mirror_pypi_$$" "/tmp/best_mirror_npm_$$" 2>/dev/null
    exit 0
fi

# 交互式选择个人服务器
select_personal_servers() {
    if [ ${#PERSONAL_SERVER_IDS[@]} -eq 0 ]; then
        error "未配置任何个人服务器"
        return 1
    fi

    info "请选择要推送的服务器环境:"
    echo "-----------------------------------"
    echo "1) 全部环境"
    echo "2) 正式环境 (${PROD_HOST})"
    echo "3) 测试环境 (${TEST_HOST})"
    echo "-----------------------------------"

    local selection
    read -p "请输入选项编号 [3]: " selection
    selection=${selection:-3}

    case $selection in
        1)
            for server_id in "${PERSONAL_SERVER_IDS[@]}"; do
                SELECTED_PERSONAL_SERVERS+=("$server_id")
            done
            info "已选择: 全部环境"
            ;;
        2)
            SELECTED_PERSONAL_SERVERS=("prod")
            info "已选择: 正式环境 (${PROD_HOST})"
            ;;
        3)
            SELECTED_PERSONAL_SERVERS=("test")
            info "已选择: 测试环境 (${TEST_HOST})"
            ;;
        *)
            error "无效的选项编号，使用默认选项：测试环境"
            SELECTED_PERSONAL_SERVERS=("test")
            info "已选择: 测试环境 (${TEST_HOST})"
            ;;
    esac

    return 0
}

# 交互式选择构建镜像类型
select_build_type() {
    info "请选择要构建的镜像类型:"
    echo "-----------------------------------"
    echo "1) 构建所有镜像 (服务器+Web界面)"
    echo "2) 仅构建服务器镜像"
    echo "3) 仅构建Web界面镜像"
    echo "-----------------------------------"

    local selection
    read -p "请输入选项编号 [1]: " selection
    selection=${selection:-1}

    case $selection in
        1)
            BUILD_ALL=true
            BUILD_SERVER=false
            BUILD_WEB=false
            info "已选择: 构建所有镜像"
            ;;
        2)
            BUILD_ALL=false
            BUILD_SERVER=true
            BUILD_WEB=false
            info "已选择: 仅构建服务器镜像"
            ;;
        3)
            BUILD_ALL=false
            BUILD_SERVER=false
            BUILD_WEB=true
            info "已选择: 仅构建Web界面镜像"
            ;;
        *)
            error "无效的选项编号，使用默认选项：构建所有镜像"
            BUILD_ALL=true
            BUILD_SERVER=false
            BUILD_WEB=false
            info "已选择: 构建所有镜像"
            ;;
    esac

    return 0
}

# 注意：清理选项功能已移至独立的 docker-cleanup.sh 脚本

# 交互式选择环境
select_environment() {
    info "请选择构建模式:"
    echo "-----------------------------------"
    echo "1) 本地调试模式 (使用当前系统架构)"
    echo "2) 服务器部署模式 (amd64架构)"
    echo "-----------------------------------"
    echo "说明:"
    echo "  本地调试: 快速原生构建，适合本地测试"
    echo "  服务器部署: amd64跨平台构建，适合生产部署"
    echo "-----------------------------------"

    local selection
    read -p "请输入选项编号 [1]: " selection
    selection=${selection:-1}

    case $selection in
        1)
            # 本地调试模式 - 使用当前系统架构
            unset DOCKER_DEFAULT_PLATFORM
            unset TARGET_PLATFORM
            local host_arch=$(uname -m)
            case $host_arch in
                "x86_64") export TARGET_PLATFORM="linux/amd64" ;;
                "aarch64"|"arm64") export TARGET_PLATFORM="linux/arm64" ;;
                *) export TARGET_PLATFORM="linux/$host_arch" ;;
            esac
            info "已选择: 本地调试模式 (使用当前系统架构: $TARGET_PLATFORM)"
            ;;
        2)
            # 服务器部署模式 - 强制amd64
            export DOCKER_DEFAULT_PLATFORM="linux/amd64"
            export TARGET_PLATFORM="linux/amd64"
            info "已选择: 服务器部署模式 (amd64跨平台构建)"
            ;;
        *)
            error "无效的选项编号，使用默认选项：本地调试模式"
            unset DOCKER_DEFAULT_PLATFORM
            unset TARGET_PLATFORM
            local host_arch=$(uname -m)
            case $host_arch in
                "x86_64") export TARGET_PLATFORM="linux/amd64" ;;
                "aarch64"|"arm64") export TARGET_PLATFORM="linux/arm64" ;;
                *) export TARGET_PLATFORM="linux/$host_arch" ;;
            esac
            info "已选择: 本地调试模式 (使用当前系统架构: $TARGET_PLATFORM)"
            ;;
    esac

    # 分离构建和推送选择
    info "是否推送镜像?"
            echo "-----------------------------------"
    echo "1) 不推送 (仅构建)"
    echo "2) 推送到个人服务器"
    echo "3) 推送到GitHub Container Registry"
    echo "4) 推送到DockerHub"
            echo "-----------------------------------"

            local push_selection
            read -p "请输入选项编号 [1]: " push_selection
            push_selection=${push_selection:-1}

            case $push_selection in
                1)
            PUSH_IMAGES=false
            PUSH_PERSONAL=false
            PUSH_GITHUB=false
            PUSH_DOCKERHUB=false
            info "已选择: 不推送镜像"
            ;;
        2)
            PUSH_IMAGES=true
                    PUSH_PERSONAL=true
                    PUSH_GITHUB=false
                    PUSH_DOCKERHUB=false
                    info "已选择: 推送到个人服务器"
                    select_personal_servers || PUSH_PERSONAL=false
                    ;;
        3)
            PUSH_IMAGES=true
                    PUSH_PERSONAL=false
                    PUSH_GITHUB=true
                    PUSH_DOCKERHUB=false
                    info "已选择: 推送到GitHub Container Registry"
                    ;;
        4)
            PUSH_IMAGES=true
                    PUSH_PERSONAL=false
                    PUSH_GITHUB=false
                    PUSH_DOCKERHUB=true
                    info "已选择: 推送到DockerHub"
                    if [ -z "$DOCKERHUB_USERNAME" ]; then
                        read -p "请输入DockerHub用户名: " DOCKERHUB_USERNAME
                        if [ -z "$DOCKERHUB_USERNAME" ]; then
                            error "DockerHub用户名不能为空"
                            PUSH_DOCKERHUB=false
                    PUSH_IMAGES=false
                        fi
                    fi
                    ;;
                *)
            error "无效的选项编号，使用默认选项：不推送镜像"
            PUSH_IMAGES=false
            PUSH_PERSONAL=false
            PUSH_GITHUB=false
            PUSH_DOCKERHUB=false
            info "已选择: 不推送镜像"
            ;;
    esac

    return 0
}

# 交互式选择网络配置
select_network_config() {
    info "正在测试网络连接和镜像源..."

    # 快速模式：跳过测速直接使用默认源
    if [[ "$QUICK_MODE" == "true" ]]; then
        info "快速模式：跳过镜像源测速，使用默认镜像源"
        USE_PROXY="false"
        USE_MIRROR_SOURCES="true"

        # 使用默认镜像源
        export DEBIAN_MIRROR="http://mirrors.163.com/debian/"
        export PIP_MIRROR="https://pypi.douban.com/simple/"
        export NPM_MIRROR="https://registry.npmmirror.com"

        info "已配置默认镜像源:"
        info "  Debian: $DEBIAN_MIRROR"
        info "  PyPI: $PIP_MIRROR"
        info "  NPM: $NPM_MIRROR"
        return 0
    fi

    # 检查是否强制使用特定模式
    if [[ "$FORCE_USE_PROXY" == "true" ]]; then
        info "已指定强制使用代理模式"
        USE_PROXY="true"
        USE_MIRROR_SOURCES="false"

        # 设置代理环境变量（使用host.docker.internal以便容器访问宿主机代理）
        export https_proxy="http://host.docker.internal:${HTTP_PROXY_PORT}"
        export http_proxy="http://host.docker.internal:${HTTP_PROXY_PORT}"
        export all_proxy="socks5://host.docker.internal:${SOCKS_PROXY_PORT}"
            export HTTPS_PROXY=$https_proxy
            export HTTP_PROXY=$http_proxy
            export ALL_PROXY=$all_proxy

        info "已配置代理环境变量"
        return 0
    fi

    if [[ "$FORCE_USE_MIRROR" == "true" ]]; then
        info "已指定强制使用镜像源模式"
        USE_PROXY="false"
        USE_MIRROR_SOURCES="true"

        # 设置默认值，以防测速失败
        export DEBIAN_MIRROR="http://mirrors.163.com/debian/"
        export PIP_MIRROR="https://pypi.douban.com/simple/"
        export NPM_MIRROR="https://registry.npmmirror.com"

        # 测试并选择最佳镜像源（按顺序执行，确保输出有序）
        info "开始测试所有镜像源速度..."
        echo "注意: 此过程可能需要几十秒，请耐心等待。如需跳过测速，请使用 --quick 选项。"

        # 按顺序测试各类镜像源
        info "测试Debian镜像源..."
        select_best_mirror "debian"
        debian_mirror=$(cat "/tmp/best_mirror_debian_$$" 2>/dev/null || echo "")

        info "测试PyPI镜像源..."
        select_best_mirror "pypi"
        pypi_mirror=$(cat "/tmp/best_mirror_pypi_$$" 2>/dev/null || echo "")

        info "测试NPM镜像源..."
        select_best_mirror "npm"
        npm_mirror=$(cat "/tmp/best_mirror_npm_$$" 2>/dev/null || echo "")

        # 检查镜像源测试结果
        local mirror_test_success=false

        # 验证返回的镜像源是否有效
        if [[ -n "$debian_mirror" && "$debian_mirror" != *"---"* && "$debian_mirror" != *"序号"* ]]; then
            export DEBIAN_MIRROR="$debian_mirror"
            mirror_test_success=true
        else
            export DEBIAN_MIRROR="http://mirrors.163.com/debian/"
            warn "无法获取有效的Debian镜像源，使用默认值: $DEBIAN_MIRROR"
        fi

        if [[ -n "$pypi_mirror" && "$pypi_mirror" != *"---"* && "$pypi_mirror" != *"序号"* ]]; then
            export PIP_MIRROR="$pypi_mirror"
            mirror_test_success=true
        else
            export PIP_MIRROR="https://pypi.douban.com/simple/"
            warn "无法获取有效的PyPI镜像源，使用默认值: $PIP_MIRROR"
        fi

        if [[ -n "$npm_mirror" && "$npm_mirror" != *"---"* && "$npm_mirror" != *"序号"* ]]; then
            export NPM_MIRROR="$npm_mirror"
            mirror_test_success=true
        else
            export NPM_MIRROR="https://registry.npmmirror.com"
            warn "无法获取有效的NPM镜像源，使用默认值: $NPM_MIRROR"
        fi

        # 如果所有镜像源测试都失败，尝试使用代理模式
        if [[ "$mirror_test_success" != "true" ]]; then
            warn "所有镜像源测试失败，尝试切换到代理模式..."
            # 检测代理可用性
            if test_proxy_availability; then
                info "检测到代理可用，自动切换到代理模式"
                USE_PROXY="true"
                USE_MIRROR_SOURCES="false"

                # 设置代理环境变量
                setup_proxy_env_vars
                return 0
            else
                warn "代理也不可用，只能使用默认镜像源"
            fi
        fi

        info "镜像源测速完成，已配置镜像源环境变量"
        info "  Debian: $DEBIAN_MIRROR"
        info "  PyPI: $PIP_MIRROR"
        info "  NPM: $NPM_MIRROR"

        # 清理临时文件
        rm -f "/tmp/best_mirror_debian_$$" "/tmp/best_mirror_pypi_$$" "/tmp/best_mirror_npm_$$" 2>/dev/null
        return 0
    fi

    # 检测代理可用性
    local proxy_ok=false
    if test_proxy_availability; then
        proxy_ok=true
    fi

    # 询问用户是否使用代理
    if [[ "$AUTO_CONFIRM" != "true" && "$proxy_ok" == "true" ]]; then
    echo "-----------------------------------"
        echo "检测到代理可用。请选择网络模式:"
        echo "1) 使用代理（推荐，如果代理稳定）"
        echo "2) 使用国内镜像源（自动选择最快的）"
    echo "-----------------------------------"

        local selection
    read -p "请输入选项编号 [2]: " selection
        selection=${selection:-2}

        if [[ "$selection" == "1" ]]; then
            USE_PROXY="true"
            USE_MIRROR_SOURCES="false"
            info "已选择: 使用代理模式"
        else
            USE_PROXY="false"
            USE_MIRROR_SOURCES="true"
            info "已选择: 使用国内镜像源模式"
        fi
    elif [[ "$proxy_ok" == "true" ]]; then
        # 自动模式，代理可用时优先使用代理
        USE_PROXY="true"
        USE_MIRROR_SOURCES="false"
        info "自动选择: 使用代理模式"
    else
        # 代理不可用，使用镜像源
        USE_PROXY="false"
        USE_MIRROR_SOURCES="true"
        info "自动选择: 使用国内镜像源模式"
    fi

    # 根据选择设置环境变量
    if [[ "$USE_PROXY" == "true" ]]; then
        # 设置代理环境变量
        setup_proxy_env_vars
        info "已配置代理环境变量"
    else
        # 设置默认值，以防测速失败
        export DEBIAN_MIRROR="http://mirrors.163.com/debian/"
        export PIP_MIRROR="https://pypi.douban.com/simple/"
        export NPM_MIRROR="https://registry.npmmirror.com"

        # 测试并选择最佳镜像源（按顺序执行，确保输出有序）
        info "开始测试所有镜像源速度..."
        echo "注意: 此过程可能需要几十秒，请耐心等待。如需跳过测速，请使用 --quick 选项。"

        # 按顺序测试各类镜像源
        info "测试Debian镜像源..."
        select_best_mirror "debian"
        debian_mirror=$(cat "/tmp/best_mirror_debian_$$" 2>/dev/null || echo "")

        info "测试PyPI镜像源..."
        select_best_mirror "pypi"
        pypi_mirror=$(cat "/tmp/best_mirror_pypi_$$" 2>/dev/null || echo "")

        info "测试NPM镜像源..."
        select_best_mirror "npm"
        npm_mirror=$(cat "/tmp/best_mirror_npm_$$" 2>/dev/null || echo "")

        # 检查镜像源测试结果
        local mirror_test_success=false

        # 验证返回的镜像源是否有效
        if [[ -n "$debian_mirror" && "$debian_mirror" != *"---"* && "$debian_mirror" != *"序号"* ]]; then
            export DEBIAN_MIRROR="$debian_mirror"
            mirror_test_success=true
        else
            export DEBIAN_MIRROR="http://mirrors.163.com/debian/"
            warn "无法获取有效的Debian镜像源，使用默认值: $DEBIAN_MIRROR"
        fi

        if [[ -n "$pypi_mirror" && "$pypi_mirror" != *"---"* && "$pypi_mirror" != *"序号"* ]]; then
            export PIP_MIRROR="$pypi_mirror"
            mirror_test_success=true
        else
            export PIP_MIRROR="https://pypi.douban.com/simple/"
            warn "无法获取有效的PyPI镜像源，使用默认值: $PIP_MIRROR"
        fi

        if [[ -n "$npm_mirror" && "$npm_mirror" != *"---"* && "$npm_mirror" != *"序号"* ]]; then
            export NPM_MIRROR="$npm_mirror"
            mirror_test_success=true
        else
            export NPM_MIRROR="https://registry.npmmirror.com"
            warn "无法获取有效的NPM镜像源，使用默认值: $NPM_MIRROR"
        fi

        # 如果所有镜像源测试都失败，尝试使用代理模式
        if [[ "$mirror_test_success" != "true" ]]; then
            warn "所有镜像源测试失败，尝试切换到代理模式..."
            # 检测代理可用性
            if test_proxy_availability; then
                info "检测到代理可用，自动切换到代理模式"
                USE_PROXY="true"
                USE_MIRROR_SOURCES="false"

                # 设置代理环境变量
                setup_proxy_env_vars
                return 0
            else
                warn "代理也不可用，只能使用默认镜像源"
            fi
        fi

        info "镜像源测速完成，已配置最佳镜像源"
        info "  Debian: $DEBIAN_MIRROR"
        info "  PyPI: $PIP_MIRROR"
        info "  NPM: $NPM_MIRROR"

        # 清理临时文件
        rm -f "/tmp/best_mirror_debian_$$" "/tmp/best_mirror_pypi_$$" "/tmp/best_mirror_npm_$$" 2>/dev/null
    fi

    return 0
}

# 检测代理可用性
test_proxy_availability() {
    local proxy_ok=false

    # 测试HTTP代理（加上超时控制）
    if timeout 5 curl -s --connect-timeout 2 --max-time 3 --proxy "http://${PROXY_HOST}:${HTTP_PROXY_PORT}" https://www.google.com > /dev/null 2>&1; then
        proxy_ok=true
        info "HTTP代理可用 (${PROXY_HOST}:${HTTP_PROXY_PORT})"
    else
        warn "HTTP代理不可用 (${PROXY_HOST}:${HTTP_PROXY_PORT})"
    fi

    # 测试SOCKS代理（加上超时控制）
    if timeout 5 curl -s --connect-timeout 2 --max-time 3 --proxy "socks5://${PROXY_HOST}:${SOCKS_PROXY_PORT}" https://www.google.com > /dev/null 2>&1; then
        proxy_ok=true
        info "SOCKS代理可用 (${PROXY_HOST}:${SOCKS_PROXY_PORT})"
    else
        warn "SOCKS代理不可用 (${PROXY_HOST}:${SOCKS_PROXY_PORT})"
    fi

    return $([[ "$proxy_ok" == "true" ]] && echo 0 || echo 1)
}

# 设置代理环境变量
setup_proxy_env_vars() {
    # 设置代理环境变量（使用host.docker.internal以便容器访问宿主机代理）
    export https_proxy="http://host.docker.internal:${HTTP_PROXY_PORT}"
    export http_proxy="http://host.docker.internal:${HTTP_PROXY_PORT}"
    export all_proxy="socks5://host.docker.internal:${SOCKS_PROXY_PORT}"
    export HTTPS_PROXY=$https_proxy
    export HTTP_PROXY=$http_proxy
    export ALL_PROXY=$all_proxy
}

# 初始化优化组件
init_optimization() {
    if [[ "$USE_CACHE_OPTIMIZATION" != "true" ]]; then
        info "缓存优化已禁用，将使用标准构建"
        return 0
    fi

    info "初始化缓存优化组件..."

    # 清理旧的生成文件，确保每次运行使用新的配置
    rm -rf "$OPTIMIZATION_DIR/dockerfiles" 2>/dev/null || true

    # 创建目录结构
    mkdir -p "$OPTIMIZATION_DIR/dockerfiles"

    # 检查requirements文件是否存在
    local requirements_file="main/xiaozhi-server/requirements.txt"
    if [[ ! -f "$requirements_file" ]]; then
        warn "未找到requirements.txt文件: $requirements_file"
        warn "请确保目录结构正确，或使用--no-cache-optimization选项"

        # 尝试找到requirements.txt文件
        local req_files=$(find . -name "requirements.txt" -type f 2>/dev/null)
        if [[ -n "$req_files" ]]; then
            warn "找到以下requirements.txt文件，请选择正确的路径:"
            echo "$req_files" | sed 's/^/  - /'
        fi
        return 1
    fi

    # 检查requirements.txt内容
    local req_count=$(grep -v "^#" "$requirements_file" | grep -v "^$" | wc -l || echo 0)
    info "检测到 $req_count 个Python依赖项"

    info "缓存优化组件初始化完成（架构: ${TARGET_ARCH:-未设置}）"
}

# 构建基础依赖镜像
build_base_image() {
    if [[ "$USE_CACHE_OPTIMIZATION" != "true" ]]; then
        return 0
    fi

    info "检查 $TARGET_ARCH 架构的基础依赖镜像..."

    # 设置基础镜像名称（使用架构特定标签）
    local base_image_name="${IMAGE_PREFIX}-base:${TARGET_ARCH}"

    # 检查是否已存在基础依赖镜像
    local base_exists=false

    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$base_image_name$"; then
        base_exists=true
        info "发现已存在的基础依赖镜像: $base_image_name"
    fi

    # 如果镜像已存在且不是强制构建，则直接返回
    if [[ "$base_exists" == "true" && "$FORCE_NO_CACHE" != "true" && "$REBUILD_BASE_IMAGE" != "true" ]]; then
        info "将使用现有基础依赖镜像（$TARGET_ARCH 架构）"
        return 0
    fi

    # 如果强制重建基础镜像，则删除现有的镜像
    if [[ "$REBUILD_BASE_IMAGE" == "true" ]]; then
        info "已指定强制重建基础依赖镜像..."
        if [[ "$base_exists" == "true" ]]; then
            info "删除现有的基础依赖镜像: $base_image_name"
            docker rmi "$base_image_name" >/dev/null 2>&1 || true
        fi
    fi

    # 检查requirements文件
    local requirements_file="main/xiaozhi-server/requirements.txt"

    if [[ ! -f "$requirements_file" ]] || [[ ! -s "$requirements_file" ]]; then
        warn "requirements.txt文件不存在或为空，跳过基础镜像构建"
    return 0
    fi

    info "开始构建 $TARGET_ARCH 架构的基础依赖镜像..."

    # 创建临时构建目录
    local temp_dir=$(mktemp -d)

    # 创建基础依赖Dockerfile - 根据网络模式使用不同的模板
    if [[ "$USE_PROXY" == "true" ]]; then
        # 代理模式模板
        cat > "$temp_dir/Dockerfile.base" << EOF
FROM --platform=linux/$TARGET_ARCH python:3.10-slim

# 设置代理环境变量
ENV HTTP_PROXY=${http_proxy}
ENV HTTPS_PROXY=${https_proxy}
ENV NO_PROXY=localhost,127.0.0.1,.local

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends libopus0 ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制requirements.txt文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt && \
    pip cache purge && \
    rm -rf /tmp/* /var/tmp/*

# 清除代理设置（防止运行时受到影响）
ENV HTTP_PROXY=
ENV HTTPS_PROXY=
ENV NO_PROXY=

# 这是一个基础镜像，不需要CMD
EOF
    else
        # 镜像源模式模板
        cat > "$temp_dir/Dockerfile.base" << EOF
FROM --platform=linux/$TARGET_ARCH python:3.10-slim

# 使用最佳Debian镜像源
RUN echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid && \
    echo "deb ${DEBIAN_MIRROR} bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb ${DEBIAN_MIRROR} bullseye-updates main contrib non-free" >> /etc/apt/sources.list

# 安装系统依赖（智能重试机制）
RUN set -e && \
    for i in 1 2 3; do \
        echo "尝试安装系统依赖，第\$i次尝试..." && \
        apt-get update -y && \
        apt-get install -y --no-install-recommends libopus0 ffmpeg && \
        apt-get clean && \
        rm -rf /var/lib/apt/lists/* && \
        echo "系统依赖安装成功!" && \
        break || \
        if [ \$i -lt 3 ]; then \
            echo "安装失败，等待5秒后重试..." && \
            sleep 5; \
        else \
            echo "安装失败，已达到最大重试次数。" && \
            exit 1; \
        fi; \
    done

# 配置pip镜像源
RUN pip config set global.index-url ${PIP_MIRROR} && \
    pip config set global.trusted-host "pypi.douban.com mirrors.aliyun.com mirrors.cloud.tencent.com mirrors.huaweicloud.com pypi.org pypi.python.org mirrors.bfsu.edu.cn mirrors.tuna.tsinghua.edu.cn mirror.sjtu.edu.cn mirrors.ustc.edu.cn" && \
    pip config set global.timeout 120 && \
    pip config set global.retries 5

WORKDIR /app

# 复制requirements.txt文件
COPY requirements.txt .

# 智能安装Python依赖（自动重试机制）
RUN set -e && \
    echo "使用最佳PyPI源安装依赖: ${PIP_MIRROR}" && \
    pip install --no-cache-dir -r requirements.txt || \
    ( \
        echo "主要源安装失败，尝试备用源..." && \
        for mirror in "https://pypi.douban.com/simple/" "https://mirrors.aliyun.com/pypi/simple/" "https://mirrors.cloud.tencent.com/pypi/simple/" "https://repo.huaweicloud.com/repository/pypi/simple/" "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/" "https://pypi.org/simple/"; do \
            if [ "\${mirror}" != "${PIP_MIRROR}" ]; then \
                echo "尝试使用 \${mirror} 安装..." && \
                pip install --no-cache-dir -i \${mirror} -r requirements.txt && \
                echo "使用 \${mirror} 安装成功!" && \
                break; \
            fi; \
        done \
    ) && \
    pip cache purge && \
    rm -rf /tmp/* /var/tmp/*

# 这是一个基础镜像，不需要CMD
EOF
    fi

    # 复制requirements文件到临时目录
    cp "$requirements_file" "$temp_dir/requirements.txt"

    # 构建基础依赖镜像
    info "构建基础依赖镜像: $base_image_name"
    local build_args=(
        "--platform=linux/$TARGET_ARCH"
        "-t" "$base_image_name"
        "-f" "$temp_dir/Dockerfile.base"
    )

    if [[ "$FORCE_NO_CACHE" == "true" ]]; then
        build_args+=("--no-cache")
        info "强制重新构建基础依赖镜像（禁用缓存）"
    fi

    info "构建过程可能需要较长时间，请耐心等待..."
    if ! docker buildx build "${build_args[@]}" "$temp_dir"; then
        error "基础依赖镜像构建失败: $base_image_name"
        warn "如果是网络问题，可以尝试重新运行并使用 --rebuild-base-image 选项"
        rm -rf "$temp_dir"
        return 1
    fi

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示构建结果
    local base_size=$(docker images --format "{{.Size}}" "$base_image_name" 2>/dev/null || echo "未知")
    info "基础依赖镜像构建完成: $base_image_name (大小: $base_size)"

    return 0
}

# 构建Web基础镜像
build_web_base_images() {
    if [[ "$USE_CACHE_OPTIMIZATION" != "true" ]]; then
        return 0
    fi

    info "检查 $TARGET_ARCH 架构的Web基础镜像..."

    # 设置Web基础镜像名称
    local frontend_base_image="${IMAGE_PREFIX}-web-frontend-base:${TARGET_ARCH}"
    local backend_base_image="${IMAGE_PREFIX}-web-backend-base:${TARGET_ARCH}"
    local runtime_base_image="${IMAGE_PREFIX}-web-runtime-base:${TARGET_ARCH}"

    # 检查依赖文件
    local package_json="main/manager-web/package.json"
    local pom_xml="main/manager-api/pom.xml"

    if [[ ! -f "$package_json" ]]; then
        warn "package.json文件不存在: $package_json，跳过Web基础镜像构建"
        return 0
    fi

    if [[ ! -f "$pom_xml" ]]; then
        warn "pom.xml文件不存在: $pom_xml，跳过Web基础镜像构建"
        return 0
    fi

    # 检查是否需要构建前端基础镜像
    local frontend_exists=false
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$frontend_base_image$"; then
        frontend_exists=true
        info "发现已存在的前端基础镜像: $frontend_base_image"
    fi

    # 检查是否需要构建后端基础镜像
    local backend_exists=false
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$backend_base_image$"; then
        backend_exists=true
        info "发现已存在的后端基础镜像: $backend_base_image"
    fi

    # 检查是否需要构建运行时基础镜像
    local runtime_exists=false
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$runtime_base_image$"; then
        runtime_exists=true
        info "发现已存在的运行时基础镜像: $runtime_base_image"
    fi

    # 如果强制重建，删除现有镜像
    if [[ "$REBUILD_BASE_IMAGE" == "true" ]]; then
        info "已指定强制重建Web基础镜像..."
        [[ "$frontend_exists" == "true" ]] && docker rmi "$frontend_base_image" >/dev/null 2>&1 || true
        [[ "$backend_exists" == "true" ]] && docker rmi "$backend_base_image" >/dev/null 2>&1 || true
        [[ "$runtime_exists" == "true" ]] && docker rmi "$runtime_base_image" >/dev/null 2>&1 || true
        frontend_exists=false
        backend_exists=false
        runtime_exists=false
    fi

    # 创建临时构建目录
    local temp_dir=$(mktemp -d)

    # 构建前端基础镜像
    if [[ "$frontend_exists" != "true" || "$FORCE_NO_CACHE" == "true" ]]; then
        info "构建前端基础镜像: $frontend_base_image"

        # 创建前端基础镜像Dockerfile
        if [[ "$USE_PROXY" == "true" ]]; then
            cat > "$temp_dir/Dockerfile.frontend-base" << EOF
FROM --platform=linux/$TARGET_ARCH node:18

# 设置代理环境变量
ENV HTTP_PROXY=${http_proxy}
ENV HTTPS_PROXY=${https_proxy}
ENV NO_PROXY=localhost,127.0.0.1,.local

WORKDIR /app
COPY package*.json ./
RUN npm install

# 清除代理设置
ENV HTTP_PROXY=
ENV HTTPS_PROXY=
ENV NO_PROXY=
EOF
        else
            cat > "$temp_dir/Dockerfile.frontend-base" << EOF
FROM --platform=linux/$TARGET_ARCH node:18

# 配置npm镜像源
RUN npm config set registry ${NPM_MIRROR} && \
    npm config set timeout 120000 && \
    npm config set fetch-retries 5

WORKDIR /app
COPY package*.json ./
RUN npm install
EOF
        fi

        # 复制package.json文件
        cp "$package_json" "$temp_dir/"
        [[ -f "main/manager-web/package-lock.json" ]] && cp "main/manager-web/package-lock.json" "$temp_dir/"

        # 构建前端基础镜像
        local frontend_build_args=(
            "--platform=linux/$TARGET_ARCH"
            "-t" "$frontend_base_image"
            "-f" "$temp_dir/Dockerfile.frontend-base"
        )
        [[ "$FORCE_NO_CACHE" == "true" ]] && frontend_build_args+=("--no-cache")

        if ! docker buildx build "${frontend_build_args[@]}" "$temp_dir"; then
            error "前端基础镜像构建失败: $frontend_base_image"
            rm -rf "$temp_dir"
            return 1
        fi
        info "前端基础镜像构建完成: $frontend_base_image"
    fi

    # 构建后端基础镜像
    if [[ "$backend_exists" != "true" || "$FORCE_NO_CACHE" == "true" ]]; then
        info "构建后端基础镜像: $backend_base_image"

        # 创建后端基础镜像Dockerfile
        if [[ "$USE_PROXY" == "true" ]]; then
            cat > "$temp_dir/Dockerfile.backend-base" << EOF
FROM --platform=linux/$TARGET_ARCH maven:3.9.4-eclipse-temurin-21

# 设置代理环境变量
ENV HTTP_PROXY=${http_proxy}
ENV HTTPS_PROXY=${https_proxy}
ENV NO_PROXY=localhost,127.0.0.1,.local

WORKDIR /app
COPY pom.xml .
RUN mvn dependency:go-offline -B

# 清除代理设置
ENV HTTP_PROXY=
ENV HTTPS_PROXY=
ENV NO_PROXY=
EOF
        else
            cat > "$temp_dir/Dockerfile.backend-base" << EOF
FROM --platform=linux/$TARGET_ARCH maven:3.9.4-eclipse-temurin-21

# 配置Maven镜像源
RUN mkdir -p /root/.m2 && \
    echo '<settings><mirrors><mirror><id>aliyun</id><name>aliyun maven</name><url>${MAVEN_MIRROR:-https://maven.aliyun.com/repository/public/}</url><mirrorOf>central</mirrorOf></mirror></mirrors></settings>' > /root/.m2/settings.xml

WORKDIR /app
COPY pom.xml .
RUN mvn dependency:go-offline -B
EOF
        fi

        # 复制pom.xml文件
        cp "$pom_xml" "$temp_dir/"

        # 构建后端基础镜像
        local backend_build_args=(
            "--platform=linux/$TARGET_ARCH"
            "-t" "$backend_base_image"
            "-f" "$temp_dir/Dockerfile.backend-base"
        )
        [[ "$FORCE_NO_CACHE" == "true" ]] && backend_build_args+=("--no-cache")

        if ! docker buildx build "${backend_build_args[@]}" "$temp_dir"; then
            error "后端基础镜像构建失败: $backend_base_image"
            rm -rf "$temp_dir"
            return 1
        fi
        info "后端基础镜像构建完成: $backend_base_image"
    fi

    # 构建运行时基础镜像
    if [[ "$runtime_exists" != "true" || "$FORCE_NO_CACHE" == "true" ]]; then
        info "构建运行时基础镜像: $runtime_base_image"

        # 创建运行时基础镜像Dockerfile
        cat > "$temp_dir/Dockerfile.runtime-base" << EOF
FROM --platform=linux/$TARGET_ARCH bellsoft/liberica-runtime-container:jre-21-glibc

# 安装Nginx和字体库
RUN apk update && \
    apk add --no-cache nginx bash && \
    apk add --no-cache fontconfig ttf-dejavu msttcorefonts-installer && \
    rm -rf /var/cache/apk/*

# 更新字体缓存
RUN printf 'YES\n' | update-ms-fonts && fc-cache -f -v
EOF

        # 构建运行时基础镜像
        local runtime_build_args=(
            "--platform=linux/$TARGET_ARCH"
            "-t" "$runtime_base_image"
            "-f" "$temp_dir/Dockerfile.runtime-base"
        )
        [[ "$FORCE_NO_CACHE" == "true" ]] && runtime_build_args+=("--no-cache")

        if ! docker buildx build "${runtime_build_args[@]}" "$temp_dir"; then
            error "运行时基础镜像构建失败: $runtime_base_image"
            rm -rf "$temp_dir"
            return 1
        fi
        info "运行时基础镜像构建完成: $runtime_base_image"
    fi

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示构建结果
    local frontend_size=$(docker images --format "{{.Size}}" "$frontend_base_image" 2>/dev/null || echo "未知")
    local backend_size=$(docker images --format "{{.Size}}" "$backend_base_image" 2>/dev/null || echo "未知")
    local runtime_size=$(docker images --format "{{.Size}}" "$runtime_base_image" 2>/dev/null || echo "未知")

    info "Web基础镜像构建完成:"
    info "  前端基础镜像: $frontend_base_image (大小: $frontend_size)"
    info "  后端基础镜像: $backend_base_image (大小: $backend_size)"
    info "  运行时基础镜像: $runtime_base_image (大小: $runtime_size)"

    return 0
}

# 生成使用基础依赖镜像的Dockerfile
generate_optimized_dockerfile() {
    local dockerfile="$OPTIMIZATION_DIR/dockerfiles/Dockerfile-server-optimized-$TARGET_ARCH"
    local base_image_name="${IMAGE_PREFIX}-base:${TARGET_ARCH}"

    # 创建目录
    mkdir -p "$OPTIMIZATION_DIR/dockerfiles"

    # 生成基于基础镜像的优化Dockerfile
    # 直接继承基础镜像，避免多阶段构建的复杂性
    cat > "$dockerfile" << EOF
# 基于基础镜像的优化Dockerfile - $TARGET_ARCH架构专用
# 直接继承预构建的基础镜像，包含所有系统依赖和Python依赖

FROM $base_image_name

# 设置工作目录
WORKDIR /opt/xiaozhi-esp32-server

# 复制应用代码（放在最后，充分利用Docker缓存）
COPY main/xiaozhi-server .

# 启动应用
CMD ["python", "app.py"]
EOF

    info "生成多阶段构建优化的Dockerfile: $dockerfile"
    echo "$dockerfile"
}

# 生成Web优化Dockerfile
generate_web_optimized_dockerfile() {
    local dockerfile="$OPTIMIZATION_DIR/dockerfiles/Dockerfile-web-optimized-$TARGET_ARCH"
    local frontend_base_image="${IMAGE_PREFIX}-web-frontend-base:${TARGET_ARCH}"
    local backend_base_image="${IMAGE_PREFIX}-web-backend-base:${TARGET_ARCH}"
    local runtime_base_image="${IMAGE_PREFIX}-web-runtime-base:${TARGET_ARCH}"

    # 创建目录
    mkdir -p "$OPTIMIZATION_DIR/dockerfiles"

    # 生成基于基础镜像的优化Web Dockerfile
    cat > "$dockerfile" << EOF
# Web优化Dockerfile - $TARGET_ARCH架构专用
# 使用预构建的基础镜像，包含所有依赖

# 第一阶段：构建Vue前端（使用前端基础镜像）
FROM $frontend_base_image as web-builder
WORKDIR /app
# 依赖已在基础镜像中安装，直接复制源码并构建
COPY main/manager-web .
RUN npm run build

# 第二阶段：构建Java后端（使用后端基础镜像）
FROM $backend_base_image as api-builder
WORKDIR /app
# 依赖已在基础镜像中下载，直接复制源码并构建
COPY main/manager-api/src ./src
RUN mvn clean package -Dmaven.test.skip=true

# 第三阶段：构建最终镜像（使用运行时基础镜像）
FROM $runtime_base_image

# 配置Nginx
COPY docs/docker/nginx.conf /etc/nginx/nginx.conf

# 复制前端构建产物
COPY --from=web-builder /app/dist /usr/share/nginx/html

# 复制Java后端JAR包
COPY --from=api-builder /app/target/xiaozhi-esp32-api.jar /app/xiaozhi-esp32-api.jar

# 暴露端口
EXPOSE 8002

# 启动脚本
COPY docs/docker/start.sh /start.sh
RUN chmod +x /start.sh
CMD ["/start.sh"]
EOF

    info "生成Web优化Dockerfile: $dockerfile"
    echo "$dockerfile"
}

# 显示缓存使用情况信息
show_cache_usage() {
    if [[ "$USE_CACHE_OPTIMIZATION" != "true" ]]; then
        info "缓存优化已禁用，未使用基础依赖镜像"
        return 0
    fi

    # 设置基础镜像名称
    local base_image_name="${IMAGE_PREFIX}-base:${TARGET_ARCH}"

    info "基础依赖镜像使用情况:"
echo "-----------------------------------"
    echo "目标架构: $TARGET_ARCH"

    # 检查基础依赖镜像
    local base_exists=false
    local base_size="未知"
    local base_created="未知"

    if docker images --format "{{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -q "^$base_image_name"; then
        base_exists=true
        base_size=$(docker images --format "{{.Size}}" "$base_image_name" 2>/dev/null || echo "未知")
        base_created=$(docker images --format "{{.CreatedAt}}" "$base_image_name" 2>/dev/null || echo "未知")

        echo "基础依赖镜像:"
        echo "  - 名称: $base_image_name"
        echo "  - 大小: $base_size"
        echo "  - 创建时间: $base_created"
    else
        echo "基础依赖镜像: 未找到 ($base_image_name)"
    fi

    # 检查requirements文件
    local requirements_file="main/xiaozhi-server/requirements.txt"
    local req_count=0

    if [[ -f "$requirements_file" ]]; then
        req_count=$(grep -v "^#" "$requirements_file" | grep -v "^$" | wc -l || echo 0)
    fi

    echo "依赖项统计:"
    echo "  - Python依赖: $req_count 项"
    echo "  - 系统依赖: libopus0, ffmpeg"

    # 检查Web基础镜像
    local frontend_base_image="${IMAGE_PREFIX}-web-frontend-base:${TARGET_ARCH}"
    local backend_base_image="${IMAGE_PREFIX}-web-backend-base:${TARGET_ARCH}"
    local runtime_base_image="${IMAGE_PREFIX}-web-runtime-base:${TARGET_ARCH}"

    echo ""
    echo "Web基础镜像:"

    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$frontend_base_image$"; then
        local frontend_size=$(docker images --format "{{.Size}}" "$frontend_base_image" 2>/dev/null || echo "未知")
        echo "  - 前端基础镜像: $frontend_base_image (大小: $frontend_size)"
    else
        echo "  - 前端基础镜像: 未找到 ($frontend_base_image)"
    fi

    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$backend_base_image$"; then
        local backend_size=$(docker images --format "{{.Size}}" "$backend_base_image" 2>/dev/null || echo "未知")
        echo "  - 后端基础镜像: $backend_base_image (大小: $backend_size)"
    else
        echo "  - 后端基础镜像: 未找到 ($backend_base_image)"
    fi

    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$runtime_base_image$"; then
        local runtime_size=$(docker images --format "{{.Size}}" "$runtime_base_image" 2>/dev/null || echo "未知")
        echo "  - 运行时基础镜像: $runtime_base_image (大小: $runtime_size)"
    else
        echo "  - 运行时基础镜像: 未找到 ($runtime_base_image)"
    fi

    # 检查Web依赖文件
    local package_json="main/manager-web/package.json"
    local pom_xml="main/manager-api/pom.xml"
    local npm_deps=0
    local maven_deps=0

    if [[ -f "$package_json" ]]; then
        npm_deps=$(grep -c '".*":' "$package_json" 2>/dev/null || echo 0)
    fi
    if [[ -f "$pom_xml" ]]; then
        maven_deps=$(grep -c '<dependency>' "$pom_xml" 2>/dev/null || echo 0)
    fi

    echo ""
    echo "Web依赖项统计:"
    echo "  - NPM依赖: $npm_deps 项"
    echo "  - Maven依赖: $maven_deps 项"
    echo "  - 运行时依赖: Nginx, 字体库"
echo "-----------------------------------"

    # 提供优化建议
    if [[ "$base_exists" != "true" ]]; then
        echo "优化建议:"
        echo "  - 服务器基础依赖镜像不存在"
        echo "  - 请先运行脚本构建基础依赖镜像"
        echo "  - 或尝试使用 '--rebuild-base-image' 选项重新构建"
        echo "-----------------------------------"
    fi
}

# 创建Docker上下文函数
create_docker_context() {
    local context_name=$1
    local host=$2
    local cert_path=$3

    info "创建Docker上下文: ${context_name}"

    # 检查上下文是否已存在
    if docker context inspect $context_name &> /dev/null; then
        warn "上下文 ${context_name} 已存在，正在删除..."
        docker context rm $context_name
    fi

    # 验证证书文件是否存在
    if [ ! -f "${cert_path}/ca.pem" ]; then
        error "证书文件不存在: ${cert_path}/ca.pem"
        return 1
    fi

    if [ ! -f "${cert_path}/cert.pem" ]; then
        error "证书文件不存在: ${cert_path}/cert.pem"
        return 1
    fi

    if [ ! -f "${cert_path}/key.pem" ]; then
        error "证书文件不存在: ${cert_path}/key.pem"
        return 1
    fi

    info "证书文件验证成功，路径: ${cert_path}"

    # 创建新的上下文
    docker context create $context_name \
        --docker "host=$host,ca=$cert_path/ca.pem,cert=$cert_path/cert.pem,key=$cert_path/key.pem"

    if [ $? -eq 0 ]; then
        info "Docker上下文创建成功: ${context_name}"
        return 0
    else
        error "Docker上下文创建失败: ${context_name}"
        return 1
    fi
}



# 推送到远程服务器函数
push_to_remote_server() {
    local context_name=$1
    local image_name=$2
    local remote_image_name=$3

    info "推送镜像到远程服务器: ${context_name}"

    # 保存镜像到文件
    local image_file="${image_name//[:\/]/_}.tar"
    info "保存镜像到文件: ${image_file}"

    docker save -o $image_file $image_name
    if [ $? -ne 0 ]; then
        error "保存镜像失败: ${image_name}"
        return 1
    fi

    # 切换到远程上下文
    info "切换到远程上下文: ${context_name}"
    docker context use $context_name

    # 加载镜像到远程服务器（清除代理环境变量，避免远程服务器尝试使用本地代理）
    info "加载镜像到远程服务器: ${remote_image_name}"
    info "清除代理设置以避免远程连接问题..."
    env -u HTTP_PROXY -u HTTPS_PROXY -u http_proxy -u https_proxy docker load -i $image_file

    if [ $? -ne 0 ]; then
        error "加载镜像到远程服务器失败"
        # 切回默认上下文
        docker context use default
        rm $image_file
        return 1
    fi

    # 切回默认上下文
    docker context use default

    # 清理临时文件
    rm $image_file

    info "镜像推送成功: ${remote_image_name}"
    return 0
}

# 主执行函数
main() {
    info "开始构建流程..." # This signals the start of actual build operations

    init_optimization

    # 构建基础依赖镜像
    if [ "$BUILD_SERVER" = true ] || [ "$BUILD_ALL" = true ]; then
        build_base_image
    fi

    # 构建Web基础镜像（仅在构建Web镜像时需要）
    if [ "$BUILD_WEB" = true ] || [ "$BUILD_ALL" = true ]; then
        build_web_base_images
    fi

    # BUILD_SERVER and BUILD_WEB are already determined before main is called
    if [ "$BUILD_SERVER" = true ]; then
        SERVER_IMAGE_NAME="${IMAGE_PREFIX}:server_${IMAGE_TAG}"

        # 注意：清理功能已移至独立的 docker-cleanup.sh 脚本
        # 如需清理，请在构建前后手动执行: ./docker-cleanup.sh [light|full]

        info "开始构建服务器镜像: ${SERVER_IMAGE_NAME}"
        info "构建架构: ${TARGET_PLATFORM:-linux/$HOST_ARCH}"

        # 设置构建时的镜像源参数（使用自动测速选择的镜像源）
        PIP_MIRROR="${PIP_MIRROR:-https://pypi.org/simple}"
        NPM_MIRROR="${NPM_MIRROR:-https://registry.npmmirror.com}"

        info "使用镜像源: PyPI=${PIP_MIRROR}, NPM=${NPM_MIRROR}"
        if [[ "$USE_MIRROR_SOURCES" == "true" && -n "$DEBIAN_MIRROR" ]]; then
            info "Debian镜像源: ${DEBIAN_MIRROR}"
        fi

        local dockerfile
        # 检查优化是否可用，基础依赖镜像是否存在
            if [[ "$USE_CACHE_OPTIMIZATION" == "true" ]]; then
            # 检查基础依赖镜像是否存在
            local base_image_name="${IMAGE_PREFIX}-base:${TARGET_ARCH}"
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$base_image_name$"; then
                # 使用基础依赖镜像的优化Dockerfile
                dockerfile=$(generate_optimized_dockerfile)
                info "使用基础依赖镜像优化构建流程（目标架构: ${TARGET_ARCH:-未设置}）"

                # 显示依赖项统计
                local requirements_file="main/xiaozhi-server/requirements.txt"
                local req_count=0
                if [[ -f "$requirements_file" ]]; then
                    req_count=$(grep -v "^#" "$requirements_file" | grep -v "^$" | wc -l || echo 0)
                fi
                info "依赖项统计: $req_count 个Python依赖项, 系统依赖包括libopus0和ffmpeg"
            else
                dockerfile="Dockerfile-server"
                warn "未找到基础依赖镜像，回退到标准构建流程"
            info "使用标准构建流程（目标架构: ${TARGET_ARCH:-未设置}）"
            fi
        else
            dockerfile="Dockerfile-server"
            info "缓存优化已禁用，使用标准构建流程"
        fi

        # 确保Dockerfile存在
        if [[ ! -f "$dockerfile" ]]; then
            error "Dockerfile不存在: $dockerfile"
            warn "尝试使用标准Dockerfile"
            dockerfile="Dockerfile-server"

            if [[ ! -f "$dockerfile" ]]; then
                error "标准Dockerfile也不存在: $dockerfile"
                error "构建服务器镜像失败: 找不到Dockerfile"
                exit 1
            fi
        fi

        # 根据目标架构选择基础镜像
        local base_image="python:3.10-slim"
        if [[ "$TARGET_ARCH" == "amd64" ]]; then
            # 检查amd64基础镜像是否存在
            if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "xiaozhi-esp32-server-base:amd64"; then
                base_image="xiaozhi-esp32-server-base:amd64"
                info "使用预构建的amd64基础镜像: $base_image"
            else
                warn "amd64基础镜像不存在，使用标准Python镜像: $base_image"
            fi
        else
            info "非amd64架构($TARGET_ARCH)，使用标准Python镜像: $base_image"
        fi

        # 构建参数
        local build_cmd_args=(
            "--build-arg" "PIP_INDEX_URL=${PIP_MIRROR}"
            "--build-arg" "DEBIAN_MIRROR=${DEBIAN_MIRROR:-http://deb.debian.org/debian/}"
            "--build-arg" "BASE_IMAGE=${base_image}"
            "--build-arg" "BUILDKIT_INLINE_CACHE=1"
            "--build-arg" "TARGETARCH=${TARGET_ARCH}"
            "--load"
            "-t" "${SERVER_IMAGE_NAME}"
            "-f" "$dockerfile"
            "."
        )

        # 如果设置了强制禁用缓存
        if [[ "$FORCE_NO_CACHE" == "true" ]]; then
            build_cmd_args=("--no-cache" "${build_cmd_args[@]}")
            info "已启用强制禁用缓存，将执行全新构建"
        fi

        # 如果使用代理模式，添加代理参数
        if [[ "$USE_PROXY" == "true" ]]; then
            build_cmd_args+=(
                "--build-arg" "HTTP_PROXY=${http_proxy:-}"
                "--build-arg" "HTTPS_PROXY=${https_proxy:-}"
                "--build-arg" "NO_PROXY=localhost,127.0.0.1,.local"
            )
            info "使用代理构建: HTTP=${http_proxy:-未设置}, HTTPS=${https_proxy:-未设置}"
        else
            info "使用国内镜像源构建: PyPI=${PIP_MIRROR}"
        fi

        # 指定目标平台
        if [[ -n "$TARGET_PLATFORM" && "$TARGET_PLATFORM" != "linux/$HOST_ARCH" ]]; then
            build_cmd_args=("--platform=$TARGET_PLATFORM" "${build_cmd_args[@]}")
            info "跨平台构建: $HOST_ARCH → ${TARGET_ARCH:-未设置}"
        else
            info "原生构建: $HOST_ARCH"
        fi

        # 打印完整命令供调试
        info "执行构建命令: docker buildx build ${build_cmd_args[*]}"

        build_success=false
        if docker buildx build "${build_cmd_args[@]}"; then
            build_success=true
            info "服务器镜像构建成功: ${SERVER_IMAGE_NAME} (架构: $TARGET_ARCH)"

            # 验证镜像大小
            local img_size=$(docker images --format "{{.Size}}" "${SERVER_IMAGE_NAME}" 2>/dev/null || echo "未知")
            info "服务器镜像大小: $img_size"
        else
            error "服务器镜像构建失败，返回码: $?"
            error "请检查Docker构建日志以获取更多信息"
            exit 1
        fi

            # 推送逻辑（简化版本，保留核心功能）
            server_push_success=false

            # 推送到个人服务器
            if [ "$PUSH_PERSONAL" = true ]; then
                for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
                    if [ "$server_id" = "prod" ]; then
                        server_info="${PERSONAL_SERVER_PROD}"
                    elif [ "$server_id" = "test" ]; then
                        server_info="${PERSONAL_SERVER_TEST}"
                    else
                        error "未知的服务器ID: ${server_id}"
                        continue
                    fi

                    IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
                    CONTEXT_NAME="personal-${server_id}"
                    info "推送服务器镜像到 ${server_name}..."

                    if create_docker_context "$CONTEXT_NAME" "https://${server_host}:2376" "$server_cert"; then
                        if push_to_remote_server "$CONTEXT_NAME" "${SERVER_IMAGE_NAME}" "${SERVER_IMAGE_NAME}"; then
                            server_push_success=true
                        fi
                    fi
                done
            fi

            # 推送到GitHub
            if [ "$PUSH_GITHUB" = true ]; then
                GITHUB_SERVER_IMAGE="${GITHUB_REGISTRY}/${IMAGE_PREFIX}:server_${IMAGE_TAG}"
                info "推送服务器镜像到GitHub Container Registry..."
                if docker tag "${SERVER_IMAGE_NAME}" "${GITHUB_SERVER_IMAGE}"; then
                    if docker push "${GITHUB_SERVER_IMAGE}"; then
                        info "服务器镜像推送到GitHub Container Registry成功"
                        server_push_success=true
                    else
                        warn "服务器镜像推送到GitHub Container Registry失败"
                    fi
                fi
            fi

            # 推送到DockerHub
            if [ "$PUSH_DOCKERHUB" = true ]; then
                DOCKERHUB_SERVER_IMAGE="${DOCKERHUB_REGISTRY}/${DOCKERHUB_USERNAME}/${IMAGE_PREFIX}:server_${IMAGE_TAG}"
                info "推送服务器镜像到DockerHub..."
                if docker tag "${SERVER_IMAGE_NAME}" "${DOCKERHUB_SERVER_IMAGE}"; then
                    if docker push "${DOCKERHUB_SERVER_IMAGE}"; then
                        info "服务器镜像推送到DockerHub成功"
                        server_push_success=true
                    else
                        warn "服务器镜像推送到DockerHub失败"
                    fi
                fi
            fi

            # 推送完成提示
        if [ "$server_push_success" = true ]; then
                info "服务器镜像推送成功"
                info "如需清理本地镜像，请执行: ./docker-cleanup.sh xiaozhi"
        else
            info "服务器镜像构建完成，保留在本地"
            info "如需清理，请执行: ./docker-cleanup.sh [light|full]"
        fi
    fi

    # Web镜像构建（支持缓存优化）
    if [ "$BUILD_WEB" = true ]; then
        WEB_IMAGE_NAME="${IMAGE_PREFIX}:web_${IMAGE_TAG}"
        # 注意：清理功能已移至独立的 docker-cleanup.sh 脚本
        info "开始构建Web界面镜像: ${WEB_IMAGE_NAME}"
        info "构建架构: ${TARGET_PLATFORM:-linux/$HOST_ARCH}"

        local web_dockerfile
        # 检查Web缓存优化是否可用
        if [[ "$USE_CACHE_OPTIMIZATION" == "true" ]]; then
            # 检查Web基础镜像是否存在
            local frontend_base_image="${IMAGE_PREFIX}-web-frontend-base:${TARGET_ARCH}"
            local backend_base_image="${IMAGE_PREFIX}-web-backend-base:${TARGET_ARCH}"
            local runtime_base_image="${IMAGE_PREFIX}-web-runtime-base:${TARGET_ARCH}"

            local all_web_bases_exist=true
            if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$frontend_base_image$"; then
                all_web_bases_exist=false
            fi
            if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$backend_base_image$"; then
                all_web_bases_exist=false
            fi
            if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$runtime_base_image$"; then
                all_web_bases_exist=false
            fi

            if [[ "$all_web_bases_exist" == "true" ]]; then
                # 使用Web基础镜像的优化Dockerfile
                web_dockerfile=$(generate_web_optimized_dockerfile)
                info "使用Web基础镜像优化构建流程（目标架构: ${TARGET_ARCH:-未设置}）"

                # 显示依赖项统计
                local package_json="main/manager-web/package.json"
                local pom_xml="main/manager-api/pom.xml"
                local npm_deps=0
                local maven_deps=0

                if [[ -f "$package_json" ]]; then
                    npm_deps=$(grep -c '".*":' "$package_json" 2>/dev/null || echo 0)
                fi
                if [[ -f "$pom_xml" ]]; then
                    maven_deps=$(grep -c '<dependency>' "$pom_xml" 2>/dev/null || echo 0)
                fi
                info "依赖项统计: $npm_deps 个NPM依赖项, $maven_deps 个Maven依赖项"
            else
                web_dockerfile="Dockerfile-web"
                warn "未找到所有Web基础镜像，回退到标准构建流程"
                info "使用标准Web构建流程（目标架构: ${TARGET_ARCH:-未设置}）"
            fi
        else
            web_dockerfile="Dockerfile-web"
            info "缓存优化已禁用，使用标准Web构建流程"
        fi

        # 确认Dockerfile存在
        if [[ ! -f "$web_dockerfile" ]]; then
            error "Web界面Dockerfile不存在: $web_dockerfile"
            warn "尝试使用标准Dockerfile"
            web_dockerfile="Dockerfile-web"

            if [[ ! -f "$web_dockerfile" ]]; then
                error "标准Web Dockerfile也不存在: $web_dockerfile"
                error "构建Web界面镜像失败: 找不到Dockerfile"
                exit 1
            fi
        fi

        # 设置构建时的镜像源参数（使用自动测速选择的镜像源）
        PIP_MIRROR="${PIP_MIRROR:-https://pypi.org/simple}"
        NPM_MIRROR="${NPM_MIRROR:-https://registry.npmjs.org/}"
        MAVEN_MIRROR="${MAVEN_MIRROR:-https://repo1.maven.org/maven2}"

        info "使用镜像源: PyPI=${PIP_MIRROR}, NPM=${NPM_MIRROR}, Maven=${MAVEN_MIRROR}"

        # 构建参数
        local web_build_args=(
            "--platform=${TARGET_PLATFORM:-linux/$HOST_ARCH}"
            "--load"
            "--build-arg" "BUILDKIT_INLINE_CACHE=1"
            "--build-arg" "NPM_MIRROR=${NPM_MIRROR}"
            "--build-arg" "MAVEN_MIRROR=${MAVEN_MIRROR}"
            "--build-arg" "PIP_INDEX_URL=${PIP_MIRROR}"
            "-t" "${WEB_IMAGE_NAME}"
            "-f" "$web_dockerfile"
            "."
        )

        # 添加代理参数（如果使用代理模式）
        if [[ "$USE_PROXY" == "true" ]]; then
            web_build_args+=(
                "--build-arg" "HTTP_PROXY=http://host.docker.internal:${HTTP_PROXY_PORT}"
                "--build-arg" "HTTPS_PROXY=http://host.docker.internal:${HTTP_PROXY_PORT}"
                "--build-arg" "NO_PROXY=localhost,127.0.0.1,.local"
            )
            info "使用代理构建Web镜像: HTTP=http://host.docker.internal:${HTTP_PROXY_PORT}, HTTPS=http://host.docker.internal:${HTTP_PROXY_PORT}"
        fi

        # 如果设置了强制禁用缓存
        if [[ "$FORCE_NO_CACHE" == "true" ]]; then
            web_build_args=("--no-cache" "${web_build_args[@]}")
            info "已启用强制禁用缓存，将执行全新构建"
        fi

        # 打印完整命令供调试
        info "执行构建命令: docker buildx build ${web_build_args[*]}"

        # 使用标准Dockerfile构建Web镜像
        build_success=false
        if docker buildx build "${web_build_args[@]}"; then
            build_success=true
            info "Web界面镜像构建成功: ${WEB_IMAGE_NAME} (架构: $TARGET_ARCH)"

            # 验证镜像大小
            local web_img_size=$(docker images --format "{{.Size}}" "${WEB_IMAGE_NAME}" 2>/dev/null || echo "未知")
            info "Web界面镜像大小: $web_img_size"

            # Web镜像推送逻辑
            web_push_success=false

            # 推送到个人服务器
            if [ "$PUSH_PERSONAL" = true ]; then
                for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
                    if [ "$server_id" = "prod" ]; then
                        server_info="${PERSONAL_SERVER_PROD}"
                    elif [ "$server_id" = "test" ]; then
                        server_info="${PERSONAL_SERVER_TEST}"
                    else
                        error "未知的服务器ID: ${server_id}"
                        continue
                    fi

                    IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
                    CONTEXT_NAME="personal-${server_id}"
                    info "推送Web界面镜像到 ${server_name}..."

                    if create_docker_context "$CONTEXT_NAME" "https://${server_host}:2376" "$server_cert"; then
                        if push_to_remote_server "$CONTEXT_NAME" "${WEB_IMAGE_NAME}" "${WEB_IMAGE_NAME}"; then
                            web_push_success=true
                        fi
                    fi
                done
            fi

            # 推送到GitHub Container Registry
            if [ "$PUSH_GITHUB" = true ]; then
                GITHUB_WEB_IMAGE="${GITHUB_REGISTRY}/${GITHUB_USERNAME}/${IMAGE_PREFIX}:web_${IMAGE_TAG}"
                info "推送Web界面镜像到GitHub Container Registry..."
                if docker tag "${WEB_IMAGE_NAME}" "${GITHUB_WEB_IMAGE}"; then
                    if docker push "${GITHUB_WEB_IMAGE}"; then
                        info "Web界面镜像推送到GitHub成功"
                        web_push_success=true
                    else
                        warn "Web界面镜像推送到GitHub失败"
                    fi
                fi
            fi

            # 推送到DockerHub
            if [ "$PUSH_DOCKERHUB" = true ]; then
                DOCKERHUB_WEB_IMAGE="${DOCKERHUB_REGISTRY}/${DOCKERHUB_USERNAME}/${IMAGE_PREFIX}:web_${IMAGE_TAG}"
                info "推送Web界面镜像到DockerHub..."
                if docker tag "${WEB_IMAGE_NAME}" "${DOCKERHUB_WEB_IMAGE}"; then
                    if docker push "${DOCKERHUB_WEB_IMAGE}"; then
                        info "Web界面镜像推送到DockerHub成功"
                        web_push_success=true
                    else
                        warn "Web界面镜像推送到DockerHub失败"
                    fi
                fi
            fi

            # 推送完成提示
            if [ "$web_push_success" = true ]; then
                info "Web界面镜像推送成功"
                info "如需清理本地镜像，请执行: ./docker-cleanup.sh xiaozhi"
            else
                info "Web界面镜像构建完成，保留在本地"
                info "如需清理，请执行: ./docker-cleanup.sh [light|full]"
            fi
        else
            error "Web界面镜像构建失败，返回码: $?"
            error "请检查Docker构建日志以获取更多信息"
            exit 1
        fi
    fi

    # 清理上下文
    if [ "$PUSH_PERSONAL" = true ]; then
        for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
            CONTEXT_NAME="personal-${server_id}"
            if docker context inspect "$CONTEXT_NAME" &> /dev/null; then
                info "清理Docker上下文: ${CONTEXT_NAME}"
                docker context use default
                docker context rm "$CONTEXT_NAME"
            fi
        done
    fi

    info "构建流程完成！"

    # 显示缓存使用情况
    show_cache_usage

    # 提示清理选项
    echo ""
    info "Docker清理选项:"
    echo "  轻量级清理: ./docker-cleanup.sh light"
    echo "  完全清理:   ./docker-cleanup.sh full"
    echo "  清理项目镜像: ./docker-cleanup.sh xiaozhi"
    echo "  查看帮助:   ./docker-cleanup.sh --help"
}

# 执行主逻辑
# 检查是否有命令行参数（使用保存的原始参数数量）
HAS_ARGS=false
if [ "$ORIGINAL_ARG_COUNT" -gt 0 ]; then
    HAS_ARGS=true
fi

# 首先进行参数解析和必要的交互式选择
if [ "$HAS_ARGS" = false ]; then
    select_build_type
    select_environment # This sets DOCKER_DEFAULT_PLATFORM, TARGET_PLATFORM, PUSH_IMAGES etc.
else
    # 命令行参数已提供，检查是否需要补充选择
    BUILD_TYPE_SPECIFIED=false
    if [ "$BUILD_SERVER" = true ] || [ "$BUILD_WEB" = true ] || [ "$BUILD_ALL" = true ]; then
        BUILD_TYPE_SPECIFIED=true
    fi

    CLEAN_OPTION_SPECIFIED=false
    if [ "${SKIP_PRE_CLEAN_SPECIFIED:-false}" = true ]; then # True if --skip-pre-clean is passed
        CLEAN_OPTION_SPECIFIED=true
    fi

    PUSH_OPTION_SPECIFIED=false
    # Check if any push-related flag that implies PUSH_IMAGES=true is set OR if --no-push is set
    if [ "$PUSH_GITHUB" = true ] || [ "$PUSH_DOCKERHUB" = true ] || [ "$PUSH_PERSONAL" = true ] || [ "$PUSH_IMAGES" = false ]; then
        PUSH_OPTION_SPECIFIED=true
    fi

    if [ "$BUILD_TYPE_SPECIFIED" = false ]; then
        select_build_type
    fi

    # 注意：清理选项已移至独立脚本，不再需要交互式选择

    # Handle push selections if PUSH_IMAGES is true and no specific push option was given
    # or if --push-personal was given but servers weren't selected (e.g. in non-interactive mode from .env)
    if [ "$PUSH_IMAGES" = true ]; then
        if [ "$PUSH_OPTION_SPECIFIED" = false ]; then # No specific push target or --no-push
            select_environment # This function includes push target selection
        elif [ "$PUSH_PERSONAL" = true ] && [ ${#SELECTED_PERSONAL_SERVERS[@]} -eq 0 ]; then
            if [ "$AUTO_CONFIRM" = true ]; then
                # 自动确认模式：默认选择测试环境
                SELECTED_PERSONAL_SERVERS=("test")
                info "自动确认模式：默认选择测试环境 (${TEST_HOST})"
            else
                select_personal_servers || PUSH_PERSONAL=false # Ensure servers are selected for personal push
            fi
        fi
    elif [ "$PUSH_IMAGES" = true ] && [ "$PUSH_OPTION_SPECIFIED" = false ]; then # PUSH_IMAGES true from .env but no specific target
         select_environment # Ask user for push destination
    fi
fi

# Prepare information needed for config display and build execution
detect_build_scenario   # Sets HOST_ARCH, TARGET_ARCH, TARGET_PLATFORM (if not already set)
select_network_config # Sets USE_PROXY, USE_MIRROR_SOURCES, and specific mirror URLs

# Finalize BUILD_SERVER and BUILD_WEB flags based on BUILD_ALL
# This ensures the config display is accurate.
if [ "$BUILD_ALL" = true ]; then
    BUILD_SERVER=true
    BUILD_WEB=true
fi

# Display configuration and ask for confirmation BEFORE any build actions
info "Docker镜像构建与推送配置:"
echo "-----------------------------------"
echo "镜像仓库地址: ${DOCKER_REGISTRY}"
echo "镜像名称前缀: ${IMAGE_PREFIX}"
echo "镜像标签: ${IMAGE_TAG}"

if [ "$BUILD_ALL" = true ]; then
    echo "构建类型: 所有镜像"
else
    echo "构建服务器镜像: ${BUILD_SERVER}"
    echo "构建Web界面镜像: ${BUILD_WEB}"
fi

echo "推送镜像: ${PUSH_IMAGES}"
echo "跳过构建前清理: ${SKIP_PRE_CLEAN}"
echo "缓存优化: ${USE_CACHE_OPTIMIZATION}"
echo "目标架构: ${TARGET_PLATFORM:-linux/$HOST_ARCH}"

echo "网络配置:"
if [ "$QUICK_MODE" = true ]; then
    echo "  模式: 快速模式（使用默认镜像源）"
    echo "  Debian镜像源: ${DEBIAN_MIRROR:-http://mirrors.163.com/debian/}" # Added default for display
    echo "  PyPI镜像源: ${PIP_MIRROR:-https://pypi.douban.com/simple/}"    # Added default for display
    echo "  NPM镜像源: ${NPM_MIRROR:-https://registry.npmmirror.com}"       # Added default for display
elif [ "$USE_PROXY" = true ]; then
    echo "  模式: 使用代理"
    echo "  代理主机: ${PROXY_HOST}"
    echo "  HTTP代理端口: ${HTTP_PROXY_PORT}"
    echo "  SOCKS代理端口: ${SOCKS_PROXY_PORT}"
elif [ "$USE_MIRROR_SOURCES" = true ]; then
    echo "  模式: 使用镜像源"
    echo "  Debian镜像源: ${DEBIAN_MIRROR}"
    echo "  PyPI镜像源: ${PIP_MIRROR}"
    echo "  NPM镜像源: ${NPM_MIRROR}"
else
    echo "  模式: 自动检测 (将根据代理可用性选择代理或镜像源)"
fi

if [ "$PUSH_PERSONAL" = true ]; then
    echo "推送到个人服务器:"
    for server_id in "${SELECTED_PERSONAL_SERVERS[@]}"; do
        if [ "$server_id" = "prod" ]; then
            server_info="${PERSONAL_SERVER_PROD}"
        elif [ "$server_id" = "test" ]; then
            server_info="${PERSONAL_SERVER_TEST}"
        fi
        IFS=':' read -r server_name server_host server_cert <<< "${server_info}"
        echo "  - $server_name ($server_host)"
    done
else
    echo "推送到个人服务器: 否"
fi

if [ "$PUSH_GITHUB" = true ]; then
    echo "推送到GitHub Container Registry: 是"
else
    echo "推送到GitHub Container Registry: 否"
fi

if [ "$PUSH_DOCKERHUB" = true ]; then
    echo "推送到DockerHub账户: ${DOCKERHUB_USERNAME}"
else
    echo "推送到DockerHub账户: 否"
fi
echo "-----------------------------------"

# 确认继续
if [ "$AUTO_CONFIRM" = true ]; then
    info "自动确认模式：跳过确认提示"
else
    read -p "是否继续? [Y/n] " -n 1 -r
    echo # Move to a new line after read
    if [[ ! $REPLY =~ ^[Yy]$ ]] && [[ ! $REPLY = "" ]]; then
        info "操作已取消"
        exit 0
    fi
fi

# 调用主函数 (now only contains build steps)
main