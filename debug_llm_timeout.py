#!/usr/bin/env python3
"""
调试LLM超时问题的脚本
"""

import asyncio
import time
import signal
import sys

class TimeoutHandler:
    def __init__(self, timeout_seconds=30):
        self.timeout_seconds = timeout_seconds
        self.timed_out = False
        
    def timeout_handler(self, signum, frame):
        self.timed_out = True
        print(f"❌ 检测到超时！操作超过 {self.timeout_seconds} 秒")
        print("🔍 可能的原因：")
        print("  1. LLM服务响应缓慢或无响应")
        print("  2. 网络连接问题")
        print("  3. 意图识别模块卡住")
        print("  4. 异步任务死锁")
        sys.exit(1)
        
    def start_timeout(self):
        signal.signal(signal.SIGALRM, self.timeout_handler)
        signal.alarm(self.timeout_seconds)
        
    def cancel_timeout(self):
        signal.alarm(0)

async def test_with_timeout():
    """测试带超时的异步操作"""
    print("🔧 开始测试异步操作超时检测...")
    
    timeout_handler = TimeoutHandler(10)  # 10秒超时
    
    try:
        timeout_handler.start_timeout()
        
        # 模拟可能卡住的操作
        print("⏰ 开始模拟可能卡住的操作...")
        await asyncio.sleep(2)  # 正常情况下2秒完成
        print("✅ 操作正常完成")
        
        timeout_handler.cancel_timeout()
        
    except Exception as e:
        timeout_handler.cancel_timeout()
        print(f"❌ 操作异常: {e}")

def main():
    """主函数"""
    print("🚀 开始LLM超时调试测试")
    print("=" * 50)
    
    print("📋 建议的调试步骤：")
    print("1. 检查LLM服务是否正常运行")
    print("2. 检查网络连接")
    print("3. 查看意图识别配置")
    print("4. 检查是否有死锁")
    print()
    
    # 运行异步测试
    asyncio.run(test_with_timeout())
    
    print("=" * 50)
    print("🎉 测试完成")
    
    print("\n💡 如果系统在意图识别阶段卡住，建议：")
    print("1. 临时禁用意图识别，直接使用function_call模式")
    print("2. 检查LLM配置和网络连接")
    print("3. 增加超时设置")

if __name__ == "__main__":
    main()
