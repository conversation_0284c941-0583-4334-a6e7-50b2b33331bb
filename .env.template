# 小智ESP32服务器环境配置模板
# 复制此文件为.env并填写您的配置

# Docker远程服务器配置
# 正式环境
PROD_HOST=your-prod-host
PROD_PORT=2376
PROD_CERT_PATH=your-prod-cert-path

# 测试环境
TEST_HOST=your-test-host
TEST_PORT=2376
TEST_CERT_PATH=your-test-cert-path

# Docker镜像仓库配置
REMOTE_REGISTRY=your-registry

# 镜像源配置
PIP_MIRROR=https://pypi.tuna.tsinghua.edu.cn/simple
NPM_MIRROR=https://registry.npmmirror.com
DOCKER_MIRROR=mirrors.tuna.tsinghua.edu.cn/docker-hub
DEBIAN_MIRROR=mirrors.tuna.tsinghua.edu.cn
DEBIAN_SECURITY_MIRROR=mirrors.tuna.tsinghua.edu.cn
MAVEN_MIRROR=https://maven.aliyun.com/repository/public
UBUNTU_MIRROR=mirrors.tuna.tsinghua.edu.cn
