#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示脚本标题
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}     小智ESP32服务器 Docker 发布脚本     ${NC}"
echo -e "${BLUE}=========================================${NC}"

# 检查是否安装了 Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 获取当前目录
CURRENT_DIR=$(pwd)
echo -e "${GREEN}当前目录: ${CURRENT_DIR}${NC}"

# 配置变量
IMAGE_PREFIX="xiaozhi-esp32-server"  # 镜像名称前缀
IMAGE_TAG="latest"                   # 镜像标签

# 加载配置文件
CONFIG_FILE="docker-publish.config"
CONFIG_TEMPLATE="docker-publish.config.template"

if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}错误: 配置文件 $CONFIG_FILE 不存在${NC}"
    echo -e "${YELLOW}提示: 请复制 $CONFIG_TEMPLATE 为 $CONFIG_FILE 并填写您的配置${NC}"
    exit 1
fi

# 加载配置
source "$CONFIG_FILE"

# 检查必要的配置变量
if [ -z "$REMOTE_REGISTRY" ] || [ -z "$PROD_HOST" ] || [ -z "$PROD_CERT_PATH" ] || [ -z "$TEST_HOST" ] || [ -z "$TEST_CERT_PATH" ]; then
    echo -e "${RED}错误: 配置文件中缺少必要的配置项${NC}"
    echo -e "${YELLOW}请确保以下变量已在配置文件中定义:${NC}"
    echo -e "REMOTE_REGISTRY, PROD_HOST, PROD_CERT_PATH, TEST_HOST, TEST_CERT_PATH"
    exit 1
fi

# 选择环境
echo -e "\n${YELLOW}请选择要发布的环境:${NC}"
echo -e "1) 正式环境 ($(echo $PROD_HOST | cut -d'/' -f3))"
echo -e "2) 测试环境 ($(echo $TEST_HOST | cut -d'/' -f3))"
echo -e "3) 两个环境都发布"
read -p "请输入选项 (1/2/3): " ENV_OPTION

# 选择镜像
echo -e "\n${YELLOW}请选择要发布的镜像:${NC}"
echo -e "1) 仅 server 镜像"
echo -e "2) 仅 web 镜像"
echo -e "3) 两个镜像都发布"
read -p "请输入选项 (1/2/3): " IMAGE_OPTION

# 确认操作
echo -e "\n${YELLOW}您选择了:${NC}"
case $ENV_OPTION in
    1) echo -e "- 发布到正式环境" ;;
    2) echo -e "- 发布到测试环境" ;;
    3) echo -e "- 发布到正式环境和测试环境" ;;
    *) echo -e "${RED}无效的选项${NC}"; exit 1 ;;
esac

case $IMAGE_OPTION in
    1) echo -e "- 发布 server 镜像" ;;
    2) echo -e "- 发布 web 镜像" ;;
    3) echo -e "- 发布 server 和 web 镜像" ;;
    *) echo -e "${RED}无效的选项${NC}"; exit 1 ;;
esac

read -p "确认以上选择? (y/n): " CONFIRM
if [[ $CONFIRM != "y" && $CONFIRM != "Y" ]]; then
    echo -e "${RED}操作已取消${NC}"
    exit 0
fi

# 构建镜像函数
build_image() {
    local image_type=$1
    local image_name="${IMAGE_PREFIX}:${image_type}_${IMAGE_TAG}"

    echo -e "\n${GREEN}开始构建 $image_type 镜像: ${image_name}...${NC}"

    if [[ $image_type == "server" ]]; then
        docker build --platform linux/amd64 -t $image_name -f ./Dockerfile-server .
    elif [[ $image_type == "web" ]]; then
        docker build --platform linux/amd64 -t $image_name -f ./Dockerfile-web .
    fi

    if [ $? -ne 0 ]; then
        echo -e "${RED}构建 $image_type 镜像失败${NC}"
        return 1
    else
        echo -e "${GREEN}构建 $image_type 镜像成功: ${image_name}${NC}"
        return 0
    fi
}

# 创建Docker上下文函数
create_docker_context() {
    local env_name=$1
    local host=$2
    local cert_path=$3

    echo -e "\n${GREEN}创建 $env_name Docker 上下文...${NC}"

    # 检查上下文是否已存在
    if docker context inspect $env_name &> /dev/null; then
        echo -e "${YELLOW}上下文 $env_name 已存在，正在删除...${NC}"
        docker context rm $env_name
    fi

    # 验证证书文件是否存在
    if [ ! -f "${cert_path}/ca.pem" ]; then
        echo -e "${RED}证书文件不存在: ${cert_path}/ca.pem${NC}"
        return 1
    fi

    if [ ! -f "${cert_path}/cert.pem" ]; then
        echo -e "${RED}证书文件不存在: ${cert_path}/cert.pem${NC}"
        return 1
    fi

    if [ ! -f "${cert_path}/key.pem" ]; then
        echo -e "${RED}证书文件不存在: ${cert_path}/key.pem${NC}"
        return 1
    fi

    echo -e "${GREEN}证书文件验证成功，路径: ${cert_path}${NC}"

    # 创建新的上下文
    docker context create $env_name \
      --docker "host=$host,ca=$cert_path/ca.pem,cert=$cert_path/cert.pem,key=$cert_path/key.pem"

    if [ $? -ne 0 ]; then
        echo -e "${RED}创建 $env_name Docker 上下文失败${NC}"
        return 1
    else
        echo -e "${GREEN}创建 $env_name Docker 上下文成功${NC}"
        return 0
    fi
}

# 保存并加载镜像函数
save_and_load_image() {
    local env_name=$1
    local image_name=$2
    local image_file="${image_name//[:\/]/_}.tar"

    echo -e "\n${GREEN}保存 $image_name 镜像到文件...${NC}"
    docker save -o $image_file $image_name

    if [ $? -ne 0 ]; then
        echo -e "${RED}保存 $image_name 镜像失败${NC}"
        return 1
    fi

    echo -e "${GREEN}切换到 $env_name 上下文...${NC}"
    docker context use $env_name

    echo -e "${GREEN}加载 $image_name 镜像到 $env_name...${NC}"
    docker load -i $image_file

    if [ $? -ne 0 ]; then
        echo -e "${RED}加载 $image_name 镜像到 $env_name 失败${NC}"
        docker context use default
        rm $image_file
        return 1
    else
        echo -e "${GREEN}加载 $image_name 镜像到 $env_name 成功${NC}"
        # 删除临时文件
        rm $image_file
        return 0
    fi
}

# 使用docker-compose重启服务函数
restart_service() {
    local env_name=$1
    local image_type=$2
    local image_name=$3
    local registry_image="${REMOTE_REGISTRY}/${IMAGE_PREFIX}:${image_type}_${IMAGE_TAG}"

    echo -e "\n${GREEN}在 $env_name 上使用docker-compose重启服务...${NC}"

    # 切换到远程服务器的工作目录
    echo -e "${GREEN}切换到 /workspace/xiaozhi 目录...${NC}"

    # 为镜像打标签（与远程仓库格式匹配）
    echo -e "${GREEN}为镜像打标签: ${registry_image}${NC}"
    docker tag $image_name $registry_image

    # 根据镜像类型重启不同的服务
    if [[ $image_type == "server" ]]; then
        # 使用docker-compose重启server服务
        echo -e "${GREEN}重启 server 服务...${NC}"
        docker compose -f /workspace/xiaozhi/docker-compose.yml down xiaozhi-esp32-server
        docker compose -f /workspace/xiaozhi/docker-compose.yml up -d xiaozhi-esp32-server
    elif [[ $image_type == "web" ]]; then
        # 使用docker-compose重启web服务
        echo -e "${GREEN}重启 web 服务...${NC}"
        docker compose -f /workspace/xiaozhi/docker-compose.yml down xiaozhi-esp32-server-web
        docker compose -f /workspace/xiaozhi/docker-compose.yml up -d xiaozhi-esp32-server-web
    fi

    if [ $? -ne 0 ]; then
        echo -e "${RED}重启服务失败${NC}"
        return 1
    else
        echo -e "${GREEN}重启服务成功${NC}"
        return 0
    fi
}

# 主流程
# 构建镜像
SERVER_IMAGE_NAME="${IMAGE_PREFIX}:server_${IMAGE_TAG}"
WEB_IMAGE_NAME="${IMAGE_PREFIX}:web_${IMAGE_TAG}"

if [[ $IMAGE_OPTION == "1" || $IMAGE_OPTION == "3" ]]; then
    build_image "server"
    SERVER_BUILD_SUCCESS=$?
else
    SERVER_BUILD_SUCCESS=0
fi

if [[ $IMAGE_OPTION == "2" || $IMAGE_OPTION == "3" ]]; then
    build_image "web"
    WEB_BUILD_SUCCESS=$?
else
    WEB_BUILD_SUCCESS=0
fi

# 如果构建失败，退出脚本
if [[ $SERVER_BUILD_SUCCESS -ne 0 || $WEB_BUILD_SUCCESS -ne 0 ]]; then
    echo -e "${RED}镜像构建失败，发布过程终止${NC}"
    exit 1
fi

# 发布到正式环境
if [[ $ENV_OPTION == "1" || $ENV_OPTION == "3" ]]; then
    create_docker_context "prod-env" "$PROD_HOST" "$PROD_CERT_PATH"

    if [[ $? -ne 0 ]]; then
        echo -e "${RED}创建正式环境上下文失败，跳过正式环境发布${NC}"
    else
        # 发布server镜像
        if [[ $IMAGE_OPTION == "1" || $IMAGE_OPTION == "3" ]]; then
            save_and_load_image "prod-env" "$SERVER_IMAGE_NAME"
            if [[ $? -eq 0 ]]; then
                restart_service "prod-env" "server" "$SERVER_IMAGE_NAME"
            fi
        fi

        # 发布web镜像
        if [[ $IMAGE_OPTION == "2" || $IMAGE_OPTION == "3" ]]; then
            save_and_load_image "prod-env" "$WEB_IMAGE_NAME"
            if [[ $? -eq 0 ]]; then
                restart_service "prod-env" "web" "$WEB_IMAGE_NAME"
            fi
        fi
    fi
fi

# 发布到测试环境
if [[ $ENV_OPTION == "2" || $ENV_OPTION == "3" ]]; then
    create_docker_context "test-env" "$TEST_HOST" "$TEST_CERT_PATH"

    if [[ $? -ne 0 ]]; then
        echo -e "${RED}创建测试环境上下文失败，跳过测试环境发布${NC}"
    else
        # 发布server镜像
        if [[ $IMAGE_OPTION == "1" || $IMAGE_OPTION == "3" ]]; then
            save_and_load_image "test-env" "$SERVER_IMAGE_NAME"
            if [[ $? -eq 0 ]]; then
                restart_service "test-env" "server" "$SERVER_IMAGE_NAME"
            fi
        fi

        # 发布web镜像
        if [[ $IMAGE_OPTION == "2" || $IMAGE_OPTION == "3" ]]; then
            save_and_load_image "test-env" "$WEB_IMAGE_NAME"
            if [[ $? -eq 0 ]]; then
                restart_service "test-env" "web" "$WEB_IMAGE_NAME"
            fi
        fi
    fi
fi

# 恢复默认上下文
docker context use default

echo -e "\n${GREEN}发布过程完成!${NC}"
echo -e "${YELLOW}提示: 请检查容器日志确认服务是否正常运行${NC}"
echo -e "正式环境: docker --context prod-env logs -f xiaozhi-esp32-server"
echo -e "测试环境: docker --context test-env logs -f xiaozhi-esp32-server"
echo -e "\n${YELLOW}提示: 如需查看docker-compose服务状态，可以使用以下命令${NC}"
echo -e "正式环境: docker --context prod-env compose -f /workspace/xiaozhi/docker-compose.yml ps"
echo -e "测试环境: docker --context test-env compose -f /workspace/xiaozhi/docker-compose.yml ps"