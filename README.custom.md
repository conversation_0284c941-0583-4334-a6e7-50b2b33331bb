# 小智ESP32服务器二次开发指南

本文档提供了如何在保持与上游代码同步的情况下进行二次开发的指南。本仓库是基于上游代码的二次开发版本，不再保留对上游Git仓库的引用，而是通过脚本直接从上游URL获取更新。

## 开发环境设置

### 1. 克隆仓库

```bash
git clone ssh://<EMAIL>:29418/xiaozhi-esp32-server
cd xiaozhi-esp32-server
```

### 2. 设置Gerrit提交钩子

确保每个提交都有Change-Id，这是Gerrit代码审查系统所需的：

```bash
gitdir=$(git rev-parse --git-dir)
scp -p -O -P 29418 <EMAIL>:hooks/commit-msg ${gitdir}/hooks/
chmod +x ${gitdir}/hooks/commit-msg
```

### 3. 使用主分支

我们的主要开发分支是`master`分支，您可以直接在这个分支上进行开发：

```bash
git checkout master
```

如果您想创建一个新的功能分支，可以基于`master`分支创建：

```bash
git checkout -b feature-branch master
```

### 4. 设置跟踪分支

为了能够使用`git pull`命令从远程仓库拉取更改，您需要设置跟踪分支：

```bash
git branch --set-upstream-to=origin/master master
```

这样，当您执行`git pull`命令时，Git会自动从origin/master分支拉取更改并合并到当前分支。

## 同步上游代码

我们提供了一个脚本`sync-upstream-code.sh`来帮助您同步上游代码，无需依赖Git分支：

```bash
./sync-upstream-code.sh [上游仓库URL] [上游分支] [本地分支]
```

默认情况下，上游仓库为`https://github.com/xinnan-tech/xiaozhi-esp32-server.git`，上游分支为`main`，本地分支为当前检出的分支。

### 脚本工作原理

1. 克隆上游仓库到临时目录
2. 比较上游仓库和当前仓库的文件
3. 识别哪些文件发生了变化
4. 将这些变化应用到您的开发分支
5. 生成一个提交，包含所有同步的更改
6. 记录上游仓库的最新提交，以便下次同步

### 使用示例

```bash
# 使用默认参数
./sync-upstream-code.sh

# 指定上游仓库、分支和本地分支
./sync-upstream-code.sh https://github.com/xinnan-tech/xiaozhi-esp32-server.git main master
```

## 提交更改

在您的开发分支上进行更改后，您可以将这些更改推送到Gerrit进行代码审查：

```bash
git push origin HEAD:refs/for/master
```

## 最佳实践

1. **定期同步上游代码**：建议每周或每两周同步一次上游代码，以减少一次性需要解决的冲突数量。

2. **保持小而频繁的提交**：每个提交应该只包含一个逻辑更改，这样更容易审查和合并。

3. **提交前测试**：在提交之前，确保您的更改不会破坏现有功能。

4. **编写清晰的提交消息**：提交消息应该清楚地说明您做了什么更改以及为什么做这些更改。

5. **解决冲突**：如果同步上游代码时出现冲突，请仔细解决这些冲突，确保您的更改不会破坏上游的功能。

## 常见问题

### Q: 如何处理同步过程中的冲突？

A: 脚本会尝试自动解决冲突，但如果无法自动解决，您需要手动解决这些冲突。解决冲突后，使用`git add`将解决的文件添加到暂存区，然后继续运行脚本。

### Q: 如何撤销同步？

A: 如果您想撤销最近的同步，可以使用`git reset --hard HEAD~1`命令。但请注意，这将丢失所有未提交的更改。

### Q: 如何查看哪些文件被同步了？

A: 您可以使用`git show`命令查看最近的同步提交中包含的文件：

```bash
git show --name-only
```
