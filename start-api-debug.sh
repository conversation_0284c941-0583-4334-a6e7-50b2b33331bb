#!/bin/bash
# 小智ESP32 API调试启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
API_DIR="$SCRIPT_DIR/main/manager-api"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}启动小智ESP32 API调试模式...${NC}"

# 检查目录是否存在
if [ ! -d "$API_DIR" ]; then
    echo -e "${RED}错误: manager-api目录不存在${NC}"
    exit 1
fi

# 进入API目录
cd "$API_DIR"

# 检查pom.xml是否存在
if [ ! -f "pom.xml" ]; then
    echo -e "${RED}错误: pom.xml文件不存在${NC}"
    exit 1
fi

# 设置Java环境变量（如果需要）
export JAVA_HOME="/Library/Java/JavaVirtualMachines/zulu-21.jdk/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

echo -e "${YELLOW}使用Java版本:${NC}"
java -version

echo -e "${YELLOW}启动API服务器（调试模式）...${NC}"

# 使用Maven Spring Boot插件启动，包含调试参数
mvn spring-boot:run \
    -Dspring-boot.run.jvmArguments="-XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8" \
    -Dspring-boot.run.profiles=dev
